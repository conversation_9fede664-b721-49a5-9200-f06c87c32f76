<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Service Worker Push</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔔 Service Worker Push Test</h1>
    
    <div>
        <button class="button" onclick="checkServiceWorker()">Check Service Worker</button>
        <button class="button" onclick="checkNotificationPermission()">Check Notification Permission</button>
        <button class="button" onclick="requestNotificationPermission()">Request Permission</button>
        <button class="button" onclick="testLocalNotification()">Test Local Notification</button>
        <button class="button" onclick="checkPushSubscription()">Check Push Subscription</button>
        <button class="button" onclick="sendTestPush()">Send Test Push (Backend)</button>
        <button class="button" onclick="clearLogs()">Clear Logs</button>
    </div>

    <div id="logs" class="log">Logs will appear here...\n</div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.textContent += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').textContent = 'Logs cleared...\n';
        }

        async function checkServiceWorker() {
            log('🔍 Checking service worker...');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.ready;
                    log(`✅ Service worker ready: ${registration.scope}`);
                    log(`📍 Active worker: ${registration.active ? 'Yes' : 'No'}`);
                    log(`📍 Installing worker: ${registration.installing ? 'Yes' : 'No'}`);
                    log(`📍 Waiting worker: ${registration.waiting ? 'Yes' : 'No'}`);
                } catch (error) {
                    log(`❌ Service worker error: ${error.message}`);
                }
            } else {
                log('❌ Service worker not supported');
            }
        }

        function checkNotificationPermission() {
            log('🔍 Checking notification permission...');
            log(`📍 Permission: ${Notification.permission}`);
        }

        async function requestNotificationPermission() {
            log('🔍 Requesting notification permission...');
            try {
                const permission = await Notification.requestPermission();
                log(`📍 Permission result: ${permission}`);
            } catch (error) {
                log(`❌ Permission request error: ${error.message}`);
            }
        }

        async function testLocalNotification() {
            log('🔍 Testing local notification...');
            
            if (Notification.permission !== 'granted') {
                log('❌ Notification permission not granted');
                return;
            }

            try {
                const notification = new Notification('🧪 Test Local Notification', {
                    body: 'This is a test notification created directly by JavaScript',
                    icon: '/hl-energy-logo-192w.png',
                    badge: '/hl-energy-logo-96w.png',
                    tag: 'test-local'
                });

                notification.onclick = () => {
                    log('📍 Local notification clicked');
                    notification.close();
                };

                log('✅ Local notification created');
            } catch (error) {
                log(`❌ Local notification error: ${error.message}`);
            }
        }

        async function checkPushSubscription() {
            log('🔍 Checking push subscription...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                const subscription = await registration.pushManager.getSubscription();
                
                if (subscription) {
                    log('✅ Push subscription exists');
                    log(`📍 Endpoint: ${subscription.endpoint.substring(0, 50)}...`);
                } else {
                    log('❌ No push subscription found');
                }
            } catch (error) {
                log(`❌ Push subscription error: ${error.message}`);
            }
        }

        async function sendTestPush() {
            log('🔍 Sending test push via backend...');
            
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('❌ No auth token found. Please login first.');
                    return;
                }

                const response = await fetch('/api/v1/push/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        message: 'Test push from SW test page'
                    })
                });

                const result = await response.json();
                log(`📍 Backend response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    log('✅ Test push sent successfully');
                    log('📍 Now check if you receive the notification...');
                } else {
                    log('❌ Test push failed');
                }
            } catch (error) {
                log(`❌ Test push error: ${error.message}`);
            }
        }

        // Listen for service worker messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                log(`📨 SW Message: ${JSON.stringify(event.data)}`);
            });
        }

        // Auto-check on load
        window.addEventListener('load', () => {
            log('🚀 Page loaded, checking initial state...');
            checkServiceWorker();
            checkNotificationPermission();
            checkPushSubscription();
        });
    </script>
</body>
</html>
