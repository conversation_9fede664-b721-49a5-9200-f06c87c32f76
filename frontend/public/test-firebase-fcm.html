<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 Firebase FCM Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.warning { background: #ffc107; color: #000; }
        .button.danger { background: #dc3545; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔥 Firebase Cloud Messaging Test</h1>
    
    <div class="container">
        <h2>📊 System Status</h2>
        <div id="status"></div>
    </div>

    <div class="container">
        <h2>🧪 Firebase FCM Tests</h2>
        <div class="grid">
            <button class="button" onclick="initializeFirebase()">1. Initialize Firebase</button>
            <button class="button" onclick="requestPermission()">2. Request Permission</button>
            <button class="button" onclick="getToken()">3. Get FCM Token</button>
            <button class="button" onclick="subscribeToFCM()">4. Subscribe to FCM</button>
            <button class="button" onclick="testFCMNotification()">5. Test FCM Notification</button>
            <button class="button danger" onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <div class="container">
        <h2>📝 Debug Logs</h2>
        <div id="logs" class="log">Firebase FCM test logs will appear here...\n</div>
    </div>

    <script type="module">
        let messaging = null;
        let currentToken = null;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '🔥';
            logs.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(`${prefix} ${message}`);
        }

        function clearLogs() {
            document.getElementById('logs').textContent = 'Logs cleared...\n';
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status');
            const permission = Notification.permission;
            const swSupported = 'serviceWorker' in navigator;
            
            let statusHTML = '';
            
            if (permission === 'granted') {
                statusHTML += '<div class="status success">✅ Notification Permission: GRANTED</div>';
            } else if (permission === 'denied') {
                statusHTML += '<div class="status error">❌ Notification Permission: DENIED</div>';
            } else {
                statusHTML += '<div class="status warning">⚠️ Notification Permission: DEFAULT (not requested)</div>';
            }
            
            statusHTML += swSupported ? 
                '<div class="status success">✅ Service Worker: SUPPORTED</div>' : 
                '<div class="status error">❌ Service Worker: NOT SUPPORTED</div>';
                
            statusHTML += messaging ? 
                '<div class="status success">✅ Firebase Messaging: INITIALIZED</div>' : 
                '<div class="status warning">⚠️ Firebase Messaging: NOT INITIALIZED</div>';
                
            statusHTML += currentToken ? 
                `<div class="status success">✅ FCM Token: ${currentToken.substring(0, 30)}...</div>` : 
                '<div class="status warning">⚠️ FCM Token: NOT OBTAINED</div>';
            
            statusDiv.innerHTML = statusHTML;
        }

        window.initializeFirebase = async function() {
            log('Initializing Firebase...');
            
            try {
                // Dynamic import Firebase
                const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                const { getMessaging, getToken, onMessage } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js');

                // Firebase config
                const firebaseConfig = {
                    apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
                    authDomain: "hlenergy-notifications.firebaseapp.com",
                    projectId: "hlenergy-notifications",
                    storageBucket: "hlenergy-notifications.firebasestorage.app",
                    messagingSenderId: "506206785168",
                    appId: "1:506206785168:web:5acaeacce5178fd2d45215",
                    measurementId: "G-JEMVPQGQ5R"
                };

                // Initialize Firebase
                const app = initializeApp(firebaseConfig);
                messaging = getMessaging(app);

                // Set up foreground message handler
                onMessage(messaging, (payload) => {
                    log(`Foreground message received: ${JSON.stringify(payload)}`, 'success');
                    
                    if (payload.notification) {
                        new Notification(payload.notification.title || 'Firebase Notification', {
                            body: payload.notification.body || 'You have a new notification',
                            icon: payload.notification.icon || '/hl-energy-logo-192w.png'
                        });
                    }
                });

                log('Firebase initialized successfully!', 'success');
                updateStatus();
            } catch (error) {
                log(`Firebase initialization failed: ${error.message}`, 'error');
            }
        };

        window.requestPermission = async function() {
            log('Requesting notification permission...');
            
            try {
                const permission = await Notification.requestPermission();
                log(`Permission result: ${permission}`, permission === 'granted' ? 'success' : 'error');
                updateStatus();
            } catch (error) {
                log(`Permission request failed: ${error.message}`, 'error');
            }
        };

        window.getToken = async function() {
            log('Getting FCM token...');
            
            if (!messaging) {
                log('Firebase not initialized. Please initialize first.', 'error');
                return;
            }

            try {
                const { getToken } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js');

                // Register the Firebase messaging service worker
                const swRegistration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
                    scope: '/firebase-cloud-messaging-push-scope'
                });

                log(`Firebase service worker registered: ${swRegistration.scope}`, 'success');

                const token = await getToken(messaging, {
                    vapidKey: 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY',
                    serviceWorkerRegistration: swRegistration
                });

                if (token) {
                    currentToken = token;
                    log(`FCM token obtained: ${token.substring(0, 50)}...`, 'success');
                    updateStatus();
                } else {
                    log('No FCM token available', 'error');
                }
            } catch (error) {
                log(`Failed to get FCM token: ${error.message}`, 'error');
            }
        };

        window.subscribeToFCM = async function() {
            log('Subscribing to FCM...');
            
            if (!currentToken) {
                log('No FCM token available. Please get token first.', 'error');
                return;
            }

            try {
                const authToken = localStorage.getItem('token');
                if (!authToken) {
                    log('No auth token found. Please login first.', 'error');
                    return;
                }

                const response = await fetch('/api/v1/push/subscribe/fcm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        token: currentToken,
                        deviceInfo: {
                            userAgent: navigator.userAgent,
                            platform: navigator.platform,
                            deviceType: 'web'
                        }
                    })
                });

                const result = await response.json();
                if (result.success) {
                    log('Successfully subscribed to FCM!', 'success');
                } else {
                    log(`FCM subscription failed: ${result.error?.message}`, 'error');
                }
            } catch (error) {
                log(`FCM subscription error: ${error.message}`, 'error');
            }
        };

        window.testFCMNotification = async function() {
            log('Sending test FCM notification...');
            
            try {
                const authToken = localStorage.getItem('token');
                if (!authToken) {
                    log('No auth token found. Please login first.', 'error');
                    return;
                }

                const response = await fetch('/api/v1/push/test/fcm', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        message: `Firebase test notification - ${new Date().toLocaleTimeString()}`
                    })
                });

                const result = await response.json();
                if (result.success) {
                    log(`Test notification sent successfully! Sent: ${result.data.sent}, Failed: ${result.data.failed}`, 'success');
                } else {
                    log(`Test notification failed: ${result.error?.message}`, 'error');
                }
            } catch (error) {
                log(`Test notification error: ${error.message}`, 'error');
            }
        };

        // Auto-update status on load
        window.addEventListener('load', updateStatus);
        document.addEventListener('visibilitychange', updateStatus);

        // Initial status update
        updateStatus();
    </script>
</body>
</html>
