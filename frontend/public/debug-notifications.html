<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔔 Notification Debug Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.warning { background: #ffc107; color: #000; }
        .button.danger { background: #dc3545; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔔 Notification Debug Tool</h1>
    
    <div class="container">
        <h2>📊 System Status</h2>
        <div id="status"></div>
    </div>

    <div class="container">
        <h2>🧪 Test Notifications</h2>
        <div class="grid">
            <button class="button" onclick="testBasicNotification()">1. Basic Notification</button>
            <button class="button" onclick="testServiceWorkerNotification()">2. Service Worker Notification</button>
            <button class="button" onclick="testPushSubscription()">3. Check Push Subscription</button>
            <button class="button" onclick="testBackendPush()">4. Backend Push Test</button>
            <button class="button warning" onclick="requestPermission()">Request Permission</button>
            <button class="button danger" onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <div class="container">
        <h2>🔍 Advanced Tests</h2>
        <div class="grid">
            <button class="button" onclick="testNotificationWithActions()">Notification with Actions</button>
            <button class="button" onclick="testSilentNotification()">Silent Notification</button>
            <button class="button" onclick="testPersistentNotification()">Persistent Notification</button>
            <button class="button" onclick="simulatePushEvent()">Simulate Push Event</button>
            <button class="button" onclick="checkServiceWorkerState()">Check SW State</button>
            <button class="button" onclick="updateServiceWorker()">Update Service Worker</button>
        </div>
    </div>

    <div class="container">
        <h2>📝 Debug Logs</h2>
        <div id="logs" class="log">Debug logs will appear here...\n</div>
    </div>

    <script>
        let logCount = 0;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '📍';
            logs.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(`${prefix} ${message}`);
            logCount++;
        }

        function clearLogs() {
            document.getElementById('logs').textContent = 'Logs cleared...\n';
            logCount = 0;
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status');
            const permission = Notification.permission;
            const swSupported = 'serviceWorker' in navigator;
            const pushSupported = 'PushManager' in window;
            
            let statusHTML = '';
            
            if (permission === 'granted') {
                statusHTML += '<div class="status success">✅ Notification Permission: GRANTED</div>';
            } else if (permission === 'denied') {
                statusHTML += '<div class="status error">❌ Notification Permission: DENIED</div>';
            } else {
                statusHTML += '<div class="status warning">⚠️ Notification Permission: DEFAULT (not requested)</div>';
            }
            
            statusHTML += swSupported ? 
                '<div class="status success">✅ Service Worker: SUPPORTED</div>' : 
                '<div class="status error">❌ Service Worker: NOT SUPPORTED</div>';
                
            statusHTML += pushSupported ? 
                '<div class="status success">✅ Push Manager: SUPPORTED</div>' : 
                '<div class="status error">❌ Push Manager: NOT SUPPORTED</div>';
                
            statusHTML += `<div class="status">📍 User Agent: ${navigator.userAgent.substring(0, 100)}...</div>`;
            statusHTML += `<div class="status">📍 Page Visibility: ${document.visibilityState}</div>`;
            statusHTML += `<div class="status">📍 Page Focus: ${document.hasFocus()}</div>`;
            
            statusDiv.innerHTML = statusHTML;
        }

        async function requestPermission() {
            log('Requesting notification permission...');
            try {
                const permission = await Notification.requestPermission();
                log(`Permission result: ${permission}`, permission === 'granted' ? 'success' : 'error');
                updateStatus();
            } catch (error) {
                log(`Permission request failed: ${error.message}`, 'error');
            }
        }

        async function testBasicNotification() {
            log('Testing basic notification...');
            
            if (Notification.permission !== 'granted') {
                log('Permission not granted, requesting...', 'warning');
                await requestPermission();
                return;
            }

            try {
                const notification = new Notification('🧪 Basic Test', {
                    body: 'This is a basic JavaScript notification',
                    icon: '/hl-energy-logo-192w.png',
                    badge: '/hl-energy-logo-96w.png',
                    tag: 'basic-test',
                    timestamp: Date.now()
                });

                notification.onclick = () => {
                    log('Basic notification clicked!', 'success');
                    notification.close();
                };

                notification.onshow = () => {
                    log('Basic notification shown!', 'success');
                };

                notification.onerror = (error) => {
                    log(`Basic notification error: ${error}`, 'error');
                };

                log('Basic notification created successfully', 'success');
            } catch (error) {
                log(`Basic notification failed: ${error.message}`, 'error');
            }
        }

        async function testServiceWorkerNotification() {
            log('Testing service worker notification...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                log('Service worker is ready');
                
                await registration.showNotification('🔧 Service Worker Test', {
                    body: 'This notification is created by the service worker',
                    icon: '/hl-energy-logo-192w.png',
                    badge: '/hl-energy-logo-96w.png',
                    tag: 'sw-test',
                    requireInteraction: true,
                    actions: [
                        { action: 'view', title: 'View' },
                        { action: 'dismiss', title: 'Dismiss' }
                    ]
                });
                
                log('Service worker notification created successfully', 'success');
            } catch (error) {
                log(`Service worker notification failed: ${error.message}`, 'error');
            }
        }

        async function testPushSubscription() {
            log('Checking push subscription...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                const subscription = await registration.pushManager.getSubscription();
                
                if (subscription) {
                    log('Push subscription exists', 'success');
                    log(`Endpoint: ${subscription.endpoint.substring(0, 50)}...`);
                    log(`Has p256dh key: ${!!subscription.getKey('p256dh')}`);
                    log(`Has auth key: ${!!subscription.getKey('auth')}`);
                } else {
                    log('No push subscription found', 'warning');
                }
            } catch (error) {
                log(`Push subscription check failed: ${error.message}`, 'error');
            }
        }

        async function testBackendPush() {
            log('Testing backend push notification...');
            
            try {
                const token = localStorage.getItem('token');
                if (!token) {
                    log('No auth token found. Please login first.', 'error');
                    return;
                }

                const response = await fetch('/api/v1/push/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        message: `Backend test notification - ${new Date().toLocaleTimeString()}`
                    })
                });

                const result = await response.json();
                log(`Backend response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    log('Backend push sent successfully', 'success');
                    log('Check if you receive the notification in 2-3 seconds...', 'warning');
                } else {
                    log('Backend push failed', 'error');
                }
            } catch (error) {
                log(`Backend push test failed: ${error.message}`, 'error');
            }
        }

        async function testNotificationWithActions() {
            log('Testing notification with actions...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.showNotification('🎯 Action Test', {
                    body: 'This notification has action buttons',
                    icon: '/hl-energy-logo-192w.png',
                    tag: 'action-test',
                    requireInteraction: true,
                    actions: [
                        { action: 'like', title: '👍 Like', icon: '/hl-energy-logo-96w.png' },
                        { action: 'reply', title: '💬 Reply', icon: '/hl-energy-logo-96w.png' },
                        { action: 'dismiss', title: '❌ Dismiss' }
                    ]
                });
                log('Action notification created', 'success');
            } catch (error) {
                log(`Action notification failed: ${error.message}`, 'error');
            }
        }

        async function testSilentNotification() {
            log('Testing silent notification...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.showNotification('🔇 Silent Test', {
                    body: 'This is a silent notification',
                    icon: '/hl-energy-logo-192w.png',
                    tag: 'silent-test',
                    silent: true
                });
                log('Silent notification created', 'success');
            } catch (error) {
                log(`Silent notification failed: ${error.message}`, 'error');
            }
        }

        async function testPersistentNotification() {
            log('Testing persistent notification...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.showNotification('📌 Persistent Test', {
                    body: 'This notification requires interaction to dismiss',
                    icon: '/hl-energy-logo-192w.png',
                    tag: 'persistent-test',
                    requireInteraction: true,
                    persistent: true
                });
                log('Persistent notification created', 'success');
            } catch (error) {
                log(`Persistent notification failed: ${error.message}`, 'error');
            }
        }

        async function simulatePushEvent() {
            log('Simulating push event...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                
                // Send a message to the service worker to simulate a push
                if (registration.active) {
                    registration.active.postMessage({
                        type: 'SIMULATE_PUSH',
                        payload: {
                            title: '🎭 Simulated Push',
                            body: 'This push event was simulated from the debug tool',
                            tag: 'simulated-push'
                        }
                    });
                    log('Push simulation message sent to service worker', 'success');
                } else {
                    log('No active service worker found', 'error');
                }
            } catch (error) {
                log(`Push simulation failed: ${error.message}`, 'error');
            }
        }

        async function checkServiceWorkerState() {
            log('Checking service worker state...');
            
            try {
                const registration = await navigator.serviceWorker.ready;
                log(`SW Scope: ${registration.scope}`);
                log(`SW Active: ${!!registration.active}`);
                log(`SW Installing: ${!!registration.installing}`);
                log(`SW Waiting: ${!!registration.waiting}`);
                log(`SW Update found: ${!!registration.waiting}`);
                
                if (registration.active) {
                    log(`SW Script URL: ${registration.active.scriptURL}`);
                    log(`SW State: ${registration.active.state}`);
                }
            } catch (error) {
                log(`SW state check failed: ${error.message}`, 'error');
            }
        }

        async function updateServiceWorker() {
            log('Updating service worker...');
            
            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                for (const registration of registrations) {
                    await registration.update();
                    log(`Updated SW: ${registration.scope}`, 'success');
                }
                log('Service worker update complete', 'success');
            } catch (error) {
                log(`SW update failed: ${error.message}`, 'error');
            }
        }

        // Listen for service worker messages
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                log(`SW Message: ${JSON.stringify(event.data)}`);
            });
        }

        // Auto-update status on load and visibility change
        window.addEventListener('load', updateStatus);
        document.addEventListener('visibilitychange', updateStatus);
        window.addEventListener('focus', updateStatus);
        window.addEventListener('blur', updateStatus);

        // Initial status update
        updateStatus();
    </script>
</body>
</html>
