// Firebase Messaging Service Worker
// This file is required by Firebase Cloud Messaging

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js')
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js')

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
  authDomain: "hlenergy-notifications.firebaseapp.com",
  projectId: "hlenergy-notifications",
  storageBucket: "hlenergy-notifications.firebasestorage.app",
  messagingSenderId: "506206785168",
  appId: "1:506206785168:web:5acaeacce5178fd2d45215",
  measurementId: "G-JEMVPQGQ5R"
}

// Initialize Firebase
firebase.initializeApp(firebaseConfig)

// Initialize Firebase messaging
const messaging = firebase.messaging()

console.log('🔥 Firebase Messaging Service Worker initialized')

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('🔥 [Firebase SW] Background message received:', payload)
  
  // Extract notification data
  const notificationTitle = payload.notification?.title || payload.data?.title || 'HLenergy Notification'
  const notificationOptions = {
    body: payload.notification?.body || payload.data?.body || 'You have a new notification from HLenergy',
    icon: payload.notification?.icon || payload.data?.icon || '/hl-energy-logo-192w.png',
    badge: payload.notification?.badge || payload.data?.badge || '/hl-energy-logo-96w.png',
    tag: payload.data?.tag || 'hlenergy-notification',
    data: payload.data || {},
    requireInteraction: payload.data?.requireInteraction === 'true' || false,
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/hl-energy-logo-96w.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ],
    vibrate: [200, 100, 200],
    timestamp: Date.now(),
    silent: false
  }

  // Add custom actions if provided
  if (payload.data?.actions) {
    try {
      notificationOptions.actions = JSON.parse(payload.data.actions)
    } catch (e) {
      console.warn('🔥 [Firebase SW] Failed to parse actions:', e)
    }
  }

  console.log('🔥 [Firebase SW] Showing notification:', notificationTitle, notificationOptions)
  
  return self.registration.showNotification(notificationTitle, notificationOptions)
})

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('🔥 [Firebase SW] Notification clicked:', event)

  event.notification.close()

  if (event.action === 'dismiss') {
    return
  }

  // Handle notification click
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      const data = event.notification.data || {}

      // Determine URL to open
      let urlToOpen = '/'
      if (event.action === 'view' || !event.action) {
        urlToOpen = data.url || '/dashboard'
      }

      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url.includes(urlToOpen) && 'focus' in client) {
          console.log('🔥 [Firebase SW] Focusing existing window')
          return client.focus()
        }
      }

      // If no window/tab is open, open a new one
      if (clients.openWindow) {
        const fullUrl = new URL(urlToOpen, self.location.origin).href
        console.log('🔥 [Firebase SW] Opening new window:', fullUrl)
        return clients.openWindow(fullUrl)
      }
    })
  )
})

// Handle notification close
self.addEventListener('notificationclose', (event) => {
  console.log('🔥 [Firebase SW] Notification closed:', event.notification.tag)
})

console.log('🔥 Firebase Messaging Service Worker setup complete')
