// Service Worker - Version 2.2.0 - Enhanced with Push Notifications
// This file is auto-generated to force PWA updates

const CACHE_NAME = 'hlenergy-v2-2-0'
const CACHE_VERSION = Date.now().toString()

self.addEventListener('install', (event) => {
  console.log('Service Worker installing - Version 2.2.0 with Push Notifications')
  self.skipWaiting()
})

self.addEventListener('activate', (event) => {
  console.log('Service Worker activating - Version 2.2.0')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      return self.clients.claim()
    })
  )
})

// Push notification event handler
self.addEventListener('push', (event) => {
  console.log('🔔 [SW] ===== PUSH EVENT RECEIVED =====')
  console.log('🔔 [SW] Push notification received!', event)
  console.log('🔔 [SW] Event data:', event.data)
  console.log('🔔 [SW] Has data:', !!event.data)
  console.log('🔔 [SW] Event type:', event.type)
  console.log('🔔 [SW] Timestamp:', new Date().toISOString())

  let notificationData = {
    title: 'HLenergy Notification',
    body: 'You have a new notification from HLenergy',
    icon: `${self.location.origin}/hl-energy-logo-192w.png`,
    badge: `${self.location.origin}/hl-energy-logo-96w.png`,
    tag: 'hlenergy-notification',
    data: {}
  }

  // Parse push data if available
  if (event.data) {
    try {
      // Get the raw text first
      const rawText = event.data.text()
      console.log('🔔 [SW] Raw data text:', rawText)

      // Try to parse as JSON
      const data = JSON.parse(rawText)
      console.log('🔔 [SW] Parsed push data:', data)
      console.log('🔔 [SW] Data type:', typeof data)
      console.log('🔔 [SW] Data keys:', Object.keys(data))

      // Merge with default notification data
      notificationData = { ...notificationData, ...data }
      console.log('🔔 [SW] Final merged data:', notificationData)
    } catch (e) {
      console.error('🔔 [SW] Error parsing push data:', e)
      console.log('🔔 [SW] Using raw text as body')
      // If JSON parsing fails, use raw text as body
      try {
        const rawText = event.data.text()
        notificationData.body = rawText || notificationData.body
      } catch (textError) {
        console.error('🔔 [SW] Error reading text:', textError)
      }
    }
  } else {
    console.log('🔔 [SW] No data in push event, using default notification')
  }

  console.log('🔔 [SW] Final notification data:', notificationData)
  console.log('🔔 [SW] About to show notification with title:', notificationData.title)

  // Show notification
  const showNotificationPromise = self.registration.showNotification(notificationData.title, {
    body: notificationData.body,
    icon: notificationData.icon || `${self.location.origin}/hl-energy-logo-192w.png`,
    badge: notificationData.badge || `${self.location.origin}/hl-energy-logo-96w.png`,
    tag: notificationData.tag,
    data: notificationData.data,
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: `${self.location.origin}/hl-energy-logo-96w.png`
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ],
    vibrate: [200, 100, 200],
    timestamp: Date.now(),
    silent: false
  }).then(() => {
    console.log('🔔 [SW] ===== NOTIFICATION DISPLAYED SUCCESSFULLY =====')
    console.log('🔔 [SW] Notification should now be visible!')
  }).catch((error) => {
    console.error('🔔 [SW] ===== NOTIFICATION DISPLAY FAILED =====')
    console.error('🔔 [SW] Failed to display notification:', error)
  })

  event.waitUntil(showNotificationPromise)
  console.log('🔔 [SW] ===== PUSH EVENT HANDLER COMPLETE =====')
})
})

// Notification click event handler
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event)

  event.notification.close()

  if (event.action === 'dismiss') {
    return
  }

  // Handle notification click
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      const data = event.notification.data || {}

      // Determine URL to open
      let urlToOpen = '/'
      if (event.action === 'view' || !event.action) {
        urlToOpen = data.url || '/dashboard'
      }

      // Check if app is already open
      for (const client of clientList) {
        if (client.url.includes(urlToOpen) && 'focus' in client) {
          return client.focus()
        }
      }

      // Open new window/tab
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen)
      }
    })
  )
})

// Notification close event handler
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event.notification.tag)

  // Track notification dismissal if needed
  const data = event.notification.data || {}
  if (data.trackDismissal) {
    // Send analytics or tracking data
    fetch('/api/v1/analytics/notification-dismissed', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tag: event.notification.tag,
        timestamp: Date.now()
      })
    }).catch(err => console.error('Failed to track notification dismissal:', err))
  }
})

// Background sync for offline notifications
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync-notifications') {
    event.waitUntil(
      // Handle background sync for notifications
      handleBackgroundSync()
    )
  }
})

// Handle background sync
async function handleBackgroundSync() {
  try {
    // Check for pending notifications when back online
    const response = await fetch('/api/v1/notifications/pending')
    if (response.ok) {
      const notifications = await response.json()

      for (const notification of notifications) {
        await self.registration.showNotification(notification.title, {
          body: notification.body,
          icon: notification.icon || '/hl-energy-logo-192w.png',
          badge: notification.badge || '/hl-energy-logo-96w.png',
          tag: notification.tag,
          data: notification.data,
          requireInteraction: notification.requireInteraction || false
        })
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// This timestamp forces the browser to recognize this as a new version
// Timestamp: ${Date.now()}
