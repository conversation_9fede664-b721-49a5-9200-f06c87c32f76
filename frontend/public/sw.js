// Service Worker - Version 3.0.0 - Firebase Cloud Messaging
// This file is auto-generated to force PWA updates

const CACHE_NAME = 'hlenergy-v3-0-0'
const CACHE_VERSION = Date.now().toString()

// Import Firebase messaging for service worker
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js')
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js')

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
  authDomain: "hlenergy-notifications.firebaseapp.com",
  projectId: "hlenergy-notifications",
  storageBucket: "hlenergy-notifications.firebasestorage.app",
  messagingSenderId: "506206785168",
  appId: "1:506206785168:web:5acaeacce5178fd2d45215",
  measurementId: "G-JEMVPQGQ5R"
}

// Initialize Firebase
firebase.initializeApp(firebaseConfig)

// Initialize Firebase messaging
const messaging = firebase.messaging()

self.addEventListener('install', (event) => {
  console.log('🔥 Service Worker installing - Version 3.0.0 with Firebase FCM')
  self.skipWaiting()
})

// Handle skip waiting message
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    console.log('🔄 [SW] Received SKIP_WAITING message, activating...')
    self.skipWaiting()
  }
})

self.addEventListener('activate', (event) => {
  console.log('🔥 Service Worker activating - Version 3.0.0 with Firebase FCM')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      return self.clients.claim()
    })
  )
})

// Firebase Cloud Messaging background message handler
messaging.onBackgroundMessage((payload) => {
  console.log('🔥 [FCM] Background message received:', payload)

  // Extract notification data
  const notificationTitle = payload.notification?.title || payload.data?.title || 'HLenergy Notification'
  const notificationOptions = {
    body: payload.notification?.body || payload.data?.body || 'You have a new notification from HLenergy',
    icon: payload.notification?.icon || payload.data?.icon || `${self.location.origin}/hl-energy-logo-192w.png`,
    badge: payload.notification?.badge || payload.data?.badge || `${self.location.origin}/hl-energy-logo-96w.png`,
    tag: payload.data?.tag || 'hlenergy-notification',
    data: payload.data || {},
    requireInteraction: payload.data?.requireInteraction === 'true' || false,
    actions: []
  }

  // Add actions if provided
  if (payload.data?.actions) {
    try {
      notificationOptions.actions = JSON.parse(payload.data.actions)
    } catch (e) {
      console.warn('🔥 [FCM] Failed to parse actions:', e)
    }
  }

  console.log('🔥 [FCM] Showing notification:', notificationTitle, notificationOptions)

  return self.registration.showNotification(notificationTitle, notificationOptions)
})



// Notification click event handler
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event)

  event.notification.close()

  if (event.action === 'dismiss') {
    return
  }

  // Handle notification click
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      const data = event.notification.data || {}

      // Determine URL to open
      let urlToOpen = '/'
      if (event.action === 'view' || !event.action) {
        urlToOpen = data.url || '/dashboard'
      }

      // Check if app is already open
      for (const client of clientList) {
        if (client.url.includes(urlToOpen) && 'focus' in client) {
          return client.focus()
        }
      }

      // Open new window/tab
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen)
      }
    })
  )
})

// Notification close event handler
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event.notification.tag)

  // Track notification dismissal if needed
  const data = event.notification.data || {}
  if (data.trackDismissal) {
    // Send analytics or tracking data
    fetch('/api/v1/analytics/notification-dismissed', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tag: event.notification.tag,
        timestamp: Date.now()
      })
    }).catch(err => console.error('Failed to track notification dismissal:', err))
  }
})

// Background sync for offline notifications
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync-notifications') {
    event.waitUntil(
      // Handle background sync for notifications
      handleBackgroundSync()
    )
  }
})

// Handle background sync
async function handleBackgroundSync() {
  try {
    // Check for pending notifications when back online
    const response = await fetch('/api/v1/notifications/pending')
    if (response.ok) {
      const notifications = await response.json()

      for (const notification of notifications) {
        await self.registration.showNotification(notification.title, {
          body: notification.body,
          icon: notification.icon || '/hl-energy-logo-192w.png',
          badge: notification.badge || '/hl-energy-logo-96w.png',
          tag: notification.tag,
          data: notification.data,
          requireInteraction: notification.requireInteraction || false
        })
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// This timestamp forces the browser to recognize this as a new version
// Timestamp: ${Date.now()}
