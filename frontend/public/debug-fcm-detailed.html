<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 FCM Detailed Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .button:hover { background: #0056b3; }
        .button.success { background: #28a745; }
        .button.warning { background: #ffc107; color: #000; }
        .button.danger { background: #dc3545; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🔥 FCM Detailed Debug Tool</h1>
    
    <div class="container">
        <h2>📊 Current Status</h2>
        <div id="status"></div>
    </div>

    <div class="container">
        <h2>🧪 Debug Tests</h2>
        <div class="grid">
            <button class="button" onclick="checkServiceWorker()">1. Check Service Worker</button>
            <button class="button" onclick="checkFirebaseConfig()">2. Check Firebase Config</button>
            <button class="button" onclick="testManualNotification()">3. Test Manual Notification</button>
            <button class="button" onclick="initializeFirebase()">4. Initialize Firebase</button>
            <button class="button" onclick="getTokenDetailed()">5. Get FCM Token (Detailed)</button>
            <button class="button danger" onclick="clearLogs()">Clear Logs</button>
        </div>
    </div>

    <div class="container">
        <h2>📝 Debug Logs</h2>
        <div id="logs" class="log">FCM detailed debug logs will appear here...\n</div>
    </div>

    <script type="module">
        let messaging = null;
        let currentToken = null;

        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : '🔥';
            logs.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(`${prefix} ${message}`);
        }

        function clearLogs() {
            document.getElementById('logs').textContent = 'Logs cleared...\n';
        }

        function updateStatus() {
            const statusDiv = document.getElementById('status');
            const permission = Notification.permission;
            const swSupported = 'serviceWorker' in navigator;
            
            let statusHTML = '';
            
            statusHTML += `<div class="status ${permission === 'granted' ? 'success' : permission === 'denied' ? 'error' : 'warning'}">
                Notification Permission: ${permission.toUpperCase()}
            </div>`;
            
            statusHTML += `<div class="status ${swSupported ? 'success' : 'error'}">
                Service Worker Support: ${swSupported ? 'YES' : 'NO'}
            </div>`;
                
            statusHTML += messaging ? 
                '<div class="status success">Firebase Messaging: INITIALIZED</div>' : 
                '<div class="status warning">Firebase Messaging: NOT INITIALIZED</div>';
                
            statusHTML += currentToken ? 
                `<div class="status success">FCM Token: ${currentToken.substring(0, 30)}...</div>` : 
                '<div class="status warning">FCM Token: NOT OBTAINED</div>';
            
            statusDiv.innerHTML = statusHTML;
        }

        window.checkServiceWorker = async function() {
            log('Checking service worker status...');
            
            try {
                if (!('serviceWorker' in navigator)) {
                    log('Service Worker not supported', 'error');
                    return;
                }

                const registrations = await navigator.serviceWorker.getRegistrations();
                log(`Found ${registrations.length} service worker registrations`);

                for (let i = 0; i < registrations.length; i++) {
                    const reg = registrations[i];
                    log(`SW ${i + 1}: ${reg.scope}`);
                    log(`  - Active: ${!!reg.active}`);
                    log(`  - Installing: ${!!reg.installing}`);
                    log(`  - Waiting: ${!!reg.waiting}`);
                    if (reg.active) {
                        log(`  - Script URL: ${reg.active.scriptURL}`);
                        log(`  - State: ${reg.active.state}`);
                    }
                }

                const ready = await navigator.serviceWorker.ready;
                log(`Ready SW scope: ${ready.scope}`, 'success');
                
            } catch (error) {
                log(`Service worker check failed: ${error.message}`, 'error');
            }
        };

        window.checkFirebaseConfig = function() {
            log('Checking Firebase configuration...');
            
            const config = {
                apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
                authDomain: "hlenergy-notifications.firebaseapp.com",
                projectId: "hlenergy-notifications",
                storageBucket: "hlenergy-notifications.firebasestorage.app",
                messagingSenderId: "506206785168",
                appId: "1:506206785168:web:5acaeacce5178fd2d45215",
                measurementId: "G-JEMVPQGQ5R"
            };

            log('Firebase Config:');
            log(`  - Project ID: ${config.projectId}`);
            log(`  - Sender ID: ${config.messagingSenderId}`);
            log(`  - App ID: ${config.appId}`);
            log(`  - Auth Domain: ${config.authDomain}`);

            const vapidKey = 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY';
            log(`VAPID Key: ${vapidKey.substring(0, 20)}...`);
        };

        window.testManualNotification = function() {
            log('Testing manual notification...');
            
            if (Notification.permission !== 'granted') {
                log('Notification permission not granted', 'error');
                return;
            }

            try {
                const notification = new Notification('Manual Test Notification', {
                    body: 'This is a manual test to verify notifications work',
                    icon: '/hl-energy-logo-192w.png',
                    tag: 'manual-test'
                });

                notification.onclick = () => {
                    log('Manual notification clicked!', 'success');
                    notification.close();
                };

                log('Manual notification created successfully', 'success');
            } catch (error) {
                log(`Manual notification failed: ${error.message}`, 'error');
            }
        };

        window.initializeFirebase = async function() {
            log('Initializing Firebase...');
            
            try {
                const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                const { getMessaging, onMessage } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js');

                const firebaseConfig = {
                    apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
                    authDomain: "hlenergy-notifications.firebaseapp.com",
                    projectId: "hlenergy-notifications",
                    storageBucket: "hlenergy-notifications.firebasestorage.app",
                    messagingSenderId: "506206785168",
                    appId: "1:506206785168:web:5acaeacce5178fd2d45215",
                    measurementId: "G-JEMVPQGQ5R"
                };

                const app = initializeApp(firebaseConfig);
                messaging = getMessaging(app);

                // Set up foreground message handler with detailed logging
                onMessage(messaging, (payload) => {
                    log('🔥 FOREGROUND MESSAGE RECEIVED!', 'success');
                    log(`Payload: ${JSON.stringify(payload, null, 2)}`);
                    
                    // Show notification manually
                    if (Notification.permission === 'granted') {
                        const title = payload.notification?.title || 'Firebase Message';
                        const options = {
                            body: payload.notification?.body || 'Message received',
                            icon: '/hl-energy-logo-192w.png'
                        };
                        
                        new Notification(title, options);
                        log('Foreground notification displayed', 'success');
                    }
                });

                log('Firebase initialized successfully!', 'success');
                updateStatus();
            } catch (error) {
                log(`Firebase initialization failed: ${error.message}`, 'error');
            }
        };

        window.getTokenDetailed = async function() {
            log('Getting FCM token with detailed logging...');
            
            if (!messaging) {
                log('Firebase not initialized. Please initialize first.', 'error');
                return;
            }

            try {
                const { getToken } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js');
                
                log('Waiting for service worker to be ready...');
                const swRegistration = await navigator.serviceWorker.ready;
                log(`Service worker ready: ${swRegistration.scope}`);
                
                if (!swRegistration.active) {
                    log('No active service worker found!', 'error');
                    return;
                }

                log('Registering Firebase messaging service worker...');
                const swRegistration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
                    scope: '/firebase-cloud-messaging-push-scope'
                });
                log(`Firebase SW registered: ${swRegistration.scope}`, 'success');

                await navigator.serviceWorker.ready;
                log('Firebase SW is ready', 'success');

                log('Getting FCM token...');
                const token = await getToken(messaging, {
                    vapidKey: 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY',
                    serviceWorkerRegistration: swRegistration
                });

                if (token) {
                    currentToken = token;
                    log(`FCM token obtained successfully!`, 'success');
                    log(`Token length: ${token.length}`);
                    log(`Token start: ${token.substring(0, 50)}`);
                    log(`Token end: ${token.substring(token.length - 50)}`);
                    updateStatus();
                } else {
                    log('No FCM token available', 'error');
                }
            } catch (error) {
                log(`Failed to get FCM token: ${error.message}`, 'error');
                log(`Error details: ${error.stack}`);
            }
        };

        // Auto-update status on load
        window.addEventListener('load', updateStatus);
        document.addEventListener('visibilitychange', updateStatus);

        // Initial status update
        updateStatus();
    </script>
</body>
</html>
