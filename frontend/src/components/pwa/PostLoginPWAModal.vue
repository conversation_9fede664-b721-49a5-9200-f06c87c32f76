<template>
  <!-- Post-Login PWA Installation Modal -->
  <div
    v-if="showModal"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
    @click.self="handleDismiss"
  >
    <div class="card bg-base-100 shadow-2xl max-w-md w-full border border-base-200/50 glass">
      <div class="card-body">
        <!-- Header -->
        <div class="flex items-center gap-3 mb-4">
          <div class="avatar">
            <div class="w-12 h-12 rounded-lg">
              <img src="/hl-energy-logo-192w.png" alt="HLenergy" />
            </div>
          </div>
          <div>
            <h3 class="card-title text-lg">Welcome to HLenergy!</h3>
            <p class="text-sm text-base-content/70">Install our app for the best experience</p>
          </div>
        </div>

        <!-- Benefits -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
              <Icon name="zap" size="sm" class="text-primary" />
            </div>
            <div>
              <p class="font-medium">Faster Performance</p>
              <p class="text-sm text-base-content/70">Lightning-fast loading and smooth interactions</p>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-secondary/20 flex items-center justify-center">
              <Icon name="wifi-off" size="sm" class="text-secondary" />
            </div>
            <div>
              <p class="font-medium">Work Offline</p>
              <p class="text-sm text-base-content/70">Access your data even without internet</p>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center">
              <Icon name="bell" size="sm" class="text-accent" />
            </div>
            <div>
              <p class="font-medium">Push Notifications</p>
              <p class="text-sm text-base-content/70">Stay updated with important alerts</p>
            </div>
          </div>
        </div>

        <!-- Manual Installation Instructions -->
        <div v-if="showManualInstructions" class="alert alert-info mb-4">
          <Icon name="info-circle" size="sm" />
          <div>
            <div class="font-medium">Manual Installation Available</div>
            <div class="text-sm">
              <span v-if="isIOS">Tap the Share button (⬆️) and select "Add to Home Screen"</span>
              <span v-else>Look for "Install" or "Add to Home Screen" in your browser menu (⋮)</span>
            </div>
          </div>
        </div>

        <!-- Direct Installation Available -->
        <div v-if="canInstallDirectly" class="alert alert-success mb-4">
          <Icon name="check-circle" size="sm" />
          <div>
            <div class="font-medium">Ready to Install</div>
            <div class="text-sm">Click the button below to install HLenergy as an app</div>
          </div>
        </div>

        <!-- Actions -->
        <div class="card-actions justify-end gap-2">
          <button
            class="btn btn-ghost"
            @click="handleDismiss"
            :disabled="isInstalling"
          >
            Maybe Later
          </button>
          <button
            class="btn btn-primary"
            @click="handleInstall"
            :disabled="isInstalling"
          >
            <span v-if="isInstalling" class="loading loading-spinner loading-sm"></span>
            <Icon v-else name="smartphone" size="sm" />
            {{ isInstalling ? 'Installing...' : 'Install App' }}
          </button>
        </div>

        <!-- Error Message -->
        <div v-if="installError" class="alert alert-error mt-4">
          <Icon name="x-circle" size="sm" />
          <span>{{ installError }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { usePWAInstall } from '@/composables/usePWAInstall'
import Icon from '@/components/common/Icon.vue'

// Props
interface Props {
  show: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  installed: []
  dismissed: []
}>()

// Composables
const {
  isInstallable,
  isInstalled,
  isInstalling,
  installError,
  canInstall,
  installPWA
} = usePWAInstall()

// Local state
const showModal = ref(false)
const localDeferredPrompt = ref(null)

// Check if recently dismissed (within 24 hours)
const wasRecentlyDismissed = () => {
  const dismissed = localStorage.getItem('pwa-post-login-dismissed')
  if (!dismissed) {
    console.log('🔍 No dismissal record found')
    return false
  }

  const dismissedTime = parseInt(dismissed)
  const dayInMs = 24 * 60 * 60 * 1000
  const timeSinceDismissal = Date.now() - dismissedTime
  const wasRecentlyDismissed = timeSinceDismissal < dayInMs

  console.log('🔍 Dismissal check:', {
    dismissedTime: new Date(dismissedTime).toISOString(),
    timeSinceDismissal: Math.round(timeSinceDismissal / (60 * 60 * 1000)) + ' hours',
    wasRecentlyDismissed
  })

  return wasRecentlyDismissed
}

// Check if PWA is supported
const pwaSupported = computed(() =>
  'serviceWorker' in navigator && 'PushManager' in window
)

// Check if we can install (either through global or local prompt)
const canInstallDirectly = computed(() =>
  canInstall.value || localDeferredPrompt.value
)

// Check if we should show manual instructions
const showManualInstructions = computed(() =>
  !canInstallDirectly.value && pwaSupported.value
)

// Check if running on iOS
const isIOS = computed(() =>
  /iPad|iPhone|iPod/.test(navigator.userAgent)
)

// Watch for prop changes
watch(() => props.show, (newValue) => {
  const shouldShow = newValue &&
                     !isInstalled.value &&
                     pwaSupported.value &&
                     !wasRecentlyDismissed()

  console.log('🔍 PostLoginPWAModal watch conditions:', {
    propsShow: newValue,
    isInstalled: isInstalled.value,
    pwaSupported: pwaSupported.value,
    canInstall: canInstall.value,
    wasRecentlyDismissed: wasRecentlyDismissed(),
    shouldShow,
    finalShowModal: shouldShow
  })

  showModal.value = shouldShow
}, { immediate: true })

// Watch for installation completion
watch(isInstalled, (newValue) => {
  if (newValue && showModal.value) {
    showModal.value = false
    emit('installed')
    emit('close')
  }
})

// Methods
const handleInstall = async () => {
  try {
    // First try the standard installation method
    if (canInstall.value) {
      const success = await installPWA()
      if (success) {
        console.log('✅ PWA installed successfully from post-login modal')
        return
      }
    }

    // Try using local deferred prompt if available
    if (localDeferredPrompt.value) {
      console.log('🎯 Using local deferred prompt for installation...')
      await localDeferredPrompt.value.prompt()
      const choiceResult = await localDeferredPrompt.value.userChoice

      if (choiceResult.outcome === 'accepted') {
        console.log('✅ PWA installed successfully using local prompt')
        localDeferredPrompt.value = null
        showModal.value = false
        emit('installed')
        emit('close')
        return
      } else {
        console.log('❌ PWA installation dismissed by user')
      }
    }

    // If standard method failed or isn't available, try to trigger beforeinstallprompt
    console.log('🔄 Attempting to trigger PWA installation manually...')

    // Check if we can trigger the beforeinstallprompt event manually
    if ('serviceWorker' in navigator) {
      // Try to register service worker if not already registered
      const registration = await navigator.serviceWorker.getRegistration()
      if (!registration) {
        console.log('📝 Registering service worker for PWA...')
        await navigator.serviceWorker.register('/sw.js')
      }

      // Wait a moment for the browser to potentially fire beforeinstallprompt
      setTimeout(async () => {
        if (canInstall.value) {
          console.log('🎉 beforeinstallprompt now available, trying installation...')
          const success = await installPWA()
          if (success) {
            console.log('✅ PWA installed successfully after manual trigger')
            return
          }
        }

        // If still can't install, show manual instructions
        console.log('ℹ️ Automatic installation not available, showing manual instructions')
        handleManualInstall()
      }, 1000)
    } else {
      // Service workers not supported, show manual instructions
      handleManualInstall()
    }
  } catch (error) {
    console.error('❌ PWA installation failed:', error)
    // Fall back to manual instructions
    handleManualInstall()
  }
}

const handleDismiss = () => {
  showModal.value = false
  emit('dismissed')
  emit('close')

  // Store dismissal to avoid showing again too soon
  localStorage.setItem('pwa-post-login-dismissed', Date.now().toString())
}

const handleManualInstall = () => {
  console.log('📱 Showing manual installation instructions')

  // Show platform-specific instructions
  const instructions = isIOS.value
    ? 'Tap the Share button (⬆️) at the bottom of Safari and select "Add to Home Screen"'
    : 'Look for "Install" or "Add to Home Screen" option in your browser menu (⋮ or ⋯)'

  // Show a more detailed alert with instructions
  if (confirm(`To install HLenergy as an app:\n\n${instructions}\n\nWould you like to try again later?`)) {
    // User wants to try again later
    handleDismiss()
  } else {
    // User doesn't want to be reminded
    showModal.value = false
    emit('dismissed')
    emit('close')

    // Store dismissal for a longer period (7 days)
    const sevenDaysFromNow = Date.now() + (7 * 24 * 60 * 60 * 1000)
    localStorage.setItem('pwa-post-login-dismissed', sevenDaysFromNow.toString())
  }
}

// Listen for beforeinstallprompt event while modal is active
const handleBeforeInstallPrompt = (e) => {
  console.log('🎉 beforeinstallprompt event captured in modal!')
  e.preventDefault()
  localDeferredPrompt.value = e
}

// Set up event listeners
onMounted(() => {
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
})

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
})
</script>

<style scoped>
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
