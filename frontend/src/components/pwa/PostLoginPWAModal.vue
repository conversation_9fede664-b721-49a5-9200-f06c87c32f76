<template>
  <!-- Post-Login PWA Installation Modal -->
  <div
    v-if="showModal"
    class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
    @click.self="handleDismiss"
  >
    <div class="card bg-base-100 shadow-2xl max-w-md w-full border border-base-200/50 glass">
      <div class="card-body">
        <!-- Header -->
        <div class="flex items-center gap-3 mb-4">
          <div class="avatar">
            <div class="w-12 h-12 rounded-lg">
              <img src="/hl-energy-logo-192w.png" alt="HLenergy" />
            </div>
          </div>
          <div>
            <h3 class="card-title text-lg">Welcome to HLenergy!</h3>
            <p class="text-sm text-base-content/70">Install our app for the best experience</p>
          </div>
        </div>

        <!-- Benefits -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
              <Icon name="zap" size="sm" class="text-primary" />
            </div>
            <div>
              <p class="font-medium">Faster Performance</p>
              <p class="text-sm text-base-content/70">Lightning-fast loading and smooth interactions</p>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-secondary/20 flex items-center justify-center">
              <Icon name="wifi-off" size="sm" class="text-secondary" />
            </div>
            <div>
              <p class="font-medium">Work Offline</p>
              <p class="text-sm text-base-content/70">Access your data even without internet</p>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center">
              <Icon name="bell" size="sm" class="text-accent" />
            </div>
            <div>
              <p class="font-medium">Push Notifications</p>
              <p class="text-sm text-base-content/70">Stay updated with important alerts</p>
            </div>
          </div>
        </div>

        <!-- Manual Installation Instructions -->
        <div v-if="showManualInstructions" class="alert alert-info mb-4">
          <Icon name="info-circle" size="sm" />
          <div>
            <div class="font-medium">Manual Installation Available</div>
            <div class="text-sm">
              <span v-if="isIOS">Tap the Share button and select "Add to Home Screen"</span>
              <span v-else>Look for "Install" or "Add to Home Screen" in your browser menu</span>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="card-actions justify-end gap-2">
          <button
            class="btn btn-ghost"
            @click="handleDismiss"
            :disabled="isInstalling"
          >
            Maybe Later
          </button>
          <button
            v-if="canInstall"
            class="btn btn-primary"
            @click="handleInstall"
            :disabled="isInstalling"
          >
            <span v-if="isInstalling" class="loading loading-spinner loading-sm"></span>
            <Icon v-else name="download" size="sm" />
            {{ isInstalling ? 'Installing...' : 'Install App' }}
          </button>
          <button
            v-else
            class="btn btn-primary"
            @click="handleManualInstall"
          >
            <Icon name="info-circle" size="sm" />
            Got It
          </button>
        </div>

        <!-- Error Message -->
        <div v-if="installError" class="alert alert-error mt-4">
          <Icon name="x-circle" size="sm" />
          <span>{{ installError }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { usePWAInstall } from '@/composables/usePWAInstall'
import Icon from '@/components/common/Icon.vue'

// Props
interface Props {
  show: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  installed: []
  dismissed: []
}>()

// Composables
const {
  isInstallable,
  isInstalled,
  isInstalling,
  installError,
  canInstall,
  installPWA
} = usePWAInstall()

// Local state
const showModal = ref(false)

// Check if recently dismissed (within 24 hours)
const wasRecentlyDismissed = () => {
  const dismissed = localStorage.getItem('pwa-post-login-dismissed')
  if (!dismissed) return false
  
  const dismissedTime = parseInt(dismissed)
  const dayInMs = 24 * 60 * 60 * 1000
  return (Date.now() - dismissedTime) < dayInMs
}

// Check if PWA is supported
const pwaSupported = computed(() =>
  'serviceWorker' in navigator && 'PushManager' in window
)

// Check if we should show manual instructions
const showManualInstructions = computed(() =>
  !canInstall.value && pwaSupported.value
)

// Check if running on iOS
const isIOS = computed(() =>
  /iPad|iPhone|iPod/.test(navigator.userAgent)
)

// Watch for prop changes
watch(() => props.show, (newValue) => {
  showModal.value = newValue &&
                   !isInstalled.value &&
                   (canInstall.value || pwaSupported.value) &&
                   !wasRecentlyDismissed()
}, { immediate: true })

// Watch for installation completion
watch(isInstalled, (newValue) => {
  if (newValue && showModal.value) {
    showModal.value = false
    emit('installed')
    emit('close')
  }
})

// Methods
const handleInstall = async () => {
  try {
    const success = await installPWA()
    if (success) {
      console.log('✅ PWA installed successfully from post-login modal')
      // The watcher will handle closing the modal and emitting events
    }
  } catch (error) {
    console.error('❌ PWA installation failed:', error)
  }
}

const handleDismiss = () => {
  showModal.value = false
  emit('dismissed')
  emit('close')

  // Store dismissal to avoid showing again too soon
  localStorage.setItem('pwa-post-login-dismissed', Date.now().toString())
}

const handleManualInstall = () => {
  // For manual installation, we just close the modal and mark as "handled"
  showModal.value = false
  emit('dismissed')
  emit('close')

  // Store dismissal but with a shorter timeout (12 hours instead of 24)
  const twelveHoursFromNow = Date.now() + (12 * 60 * 60 * 1000)
  localStorage.setItem('pwa-post-login-dismissed', twelveHoursFromNow.toString())
}
</script>

<style scoped>
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .glass {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
</style>
