import { ref, computed, onMounted, onUnmounted } from 'vue'
import { usePushNotifications } from './usePushNotifications'

export interface PWAInstallPrompt {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

export function usePWAInstall() {
  const { requestPermission, subscribe, isGranted, isSubscribed } = usePushNotifications()

  // State
  const deferredPrompt = ref<PWAInstallPrompt | null>(null)
  const isInstallable = ref(false)
  const isInstalled = ref(false)
  const isInstalling = ref(false)
  const showInstallPrompt = ref(false)
  const installError = ref<string | null>(null)

  // Computed
  const canInstall = computed(() => 
    isInstallable.value && !isInstalled.value && !isInstalling.value
  )

  const shouldShowPushPrompt = computed(() =>
    isInstalled.value && !isGranted.value && !isSubscribed.value
  )

  // Check if app is already installed
  const checkIfInstalled = () => {
    // Check if running in standalone mode (PWA installed)
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    const isInWebAppiOS = (window.navigator as any).standalone === true
    const isInWebAppChrome = window.matchMedia('(display-mode: standalone)').matches
    
    isInstalled.value = isStandalone || isInWebAppiOS || isInWebAppChrome
    
    console.log('PWA Installation Status:', {
      isStandalone,
      isInWebAppiOS,
      isInWebAppChrome,
      isInstalled: isInstalled.value
    })
  }

  // Install PWA
  const installPWA = async (): Promise<boolean> => {
    try {
      if (!deferredPrompt.value) {
        throw new Error('PWA installation not available')
      }

      isInstalling.value = true
      installError.value = null

      // Show the install prompt
      await deferredPrompt.value.prompt()

      // Wait for the user to respond to the prompt
      const choiceResult = await deferredPrompt.value.userChoice

      if (choiceResult.outcome === 'accepted') {
        console.log('✅ PWA installation accepted')
        
        // Clear the deferred prompt
        deferredPrompt.value = null
        isInstallable.value = false
        
        // Check installation status after a delay
        setTimeout(() => {
          checkIfInstalled()

          // If installed, emit event for post-login modal flow to handle
          if (isInstalled.value) {
            window.dispatchEvent(new CustomEvent('pwa-installed', {
              detail: { source: 'post-login-modal' }
            }))
          }
        }, 1000)
        
        return true
      } else {
        console.log('❌ PWA installation dismissed')
        return false
      }
    } catch (error: any) {
      console.error('PWA installation failed:', error)
      installError.value = error.message
      return false
    } finally {
      isInstalling.value = false
    }
  }

  // Prompt for push notifications after PWA installation
  const promptForPushNotifications = async () => {
    try {
      console.log('🔔 Prompting for push notifications after PWA install...')
      
      // Request notification permission
      const permissionGranted = await requestPermission()
      
      if (permissionGranted) {
        console.log('✅ Notification permission granted')
        
        // Subscribe to push notifications
        const subscribed = await subscribe()
        
        if (subscribed) {
          console.log('✅ Push notifications subscribed successfully')
          
          // Show success message
          showNotificationSuccess()
        } else {
          console.warn('⚠️ Push notification subscription failed')
        }
      } else {
        console.log('❌ Notification permission denied')
      }
    } catch (error) {
      console.error('Error setting up push notifications:', error)
    }
  }

  // Show notification success message
  const showNotificationSuccess = () => {
    // Create a temporary notification to confirm setup
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('🎉 HLenergy PWA Ready!', {
        body: 'You\'ll now receive important updates and notifications.',
        icon: '/hl-energy-logo-192w.png',
        badge: '/hl-energy-logo-96w.png',
        tag: 'pwa-setup-complete',
        requireInteraction: false
      })
    }
  }

  // Show install prompt manually
  const showInstallDialog = () => {
    showInstallPrompt.value = true
  }

  // Hide install prompt
  const hideInstallDialog = () => {
    showInstallPrompt.value = false
  }

  // Event handlers
  const handleBeforeInstallPrompt = (e: Event) => {
    console.log('🔔 PWA install prompt available')
    
    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault()
    
    // Save the event so it can be triggered later
    deferredPrompt.value = e as any
    isInstallable.value = true
    
    // Show custom install prompt after a delay
    setTimeout(() => {
      if (!isInstalled.value) {
        showInstallPrompt.value = true
      }
    }, 3000) // Show after 3 seconds
  }

  const handleAppInstalled = () => {
    console.log('✅ PWA installed successfully')
    
    // Clear the deferred prompt
    deferredPrompt.value = null
    isInstallable.value = false
    isInstalled.value = true
    showInstallPrompt.value = false
    
    // Emit event for post-login modal flow to handle
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('pwa-installed', {
        detail: { source: 'app-installed-event' }
      }))
    }, 1000)
  }

  // Initialize
  const initialize = () => {
    checkIfInstalled()
    
    // Listen for PWA install events
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
    
    // Check for display mode changes
    const mediaQuery = window.matchMedia('(display-mode: standalone)')
    mediaQuery.addEventListener('change', checkIfInstalled)
  }

  // Cleanup
  const cleanup = () => {
    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.removeEventListener('appinstalled', handleAppInstalled)
  }

  // Auto-initialize on mount
  onMounted(() => {
    initialize()
  })

  onUnmounted(() => {
    cleanup()
  })

  return {
    // State
    isInstallable,
    isInstalled,
    isInstalling,
    showInstallPrompt,
    installError,
    canInstall,
    shouldShowPushPrompt,

    // Actions
    installPWA,
    showInstallDialog,
    hideInstallDialog,
    promptForPushNotifications,

    // Utils
    checkIfInstalled,
    initialize,
    cleanup
  }
}
}

// Utility function to check if device supports PWA installation
export function isPWASupported(): boolean {
  return 'serviceWorker' in navigator && 'PushManager' in window
}

// Utility function to get PWA installation status
export function getPWAStatus() {
  const isStandalone = window.matchMedia('(display-mode: standalone)').matches
  const isInWebAppiOS = (window.navigator as any).standalone === true
  const isInWebAppChrome = window.matchMedia('(display-mode: standalone)').matches
  
  return {
    isInstalled: isStandalone || isInWebAppiOS || isInWebAppChrome,
    isStandalone,
    isInWebAppiOS,
    isInWebAppChrome,
    supportsInstallation: isPWASupported()
  }
}
