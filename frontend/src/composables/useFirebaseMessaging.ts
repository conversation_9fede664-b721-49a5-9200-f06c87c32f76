import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Firebase messaging composable for push notifications
export function useFirebaseMessaging() {
  const authStore = useAuthStore()
  
  // State
  const isSupported = ref(true) // Assume supported until proven otherwise
  const isPermissionGranted = ref(false)
  const isSubscribed = ref(false)
  const currentToken = ref<string | null>(null)
  const error = ref<string | null>(null)
  const isLoading = ref(false)

  // Computed
  const canSubscribe = computed(() =>
    isSupported.value && isPermissionGranted.value && !isSubscribed.value
  )

  const permission = computed(() =>
    isPermissionGranted.value ? 'granted' : 'default'
  )

  // Firebase messaging instance (lazy loaded)
  let messaging: any = null

  /**
   * Initialize Firebase messaging
   */
  const initializeMessaging = async () => {
    try {
      // Check if service worker and push messaging are supported
      if (!('serviceWorker' in navigator)) {
        throw new Error('Service Worker is not supported in this browser')
      }

      if (!('PushManager' in window)) {
        throw new Error('Push messaging is not supported in this browser')
      }

      console.log('🔥 Browser supports service workers and push messaging')

      // Dynamic import Firebase messaging
      console.log('🔥 Importing Firebase modules...')
      const { getMessaging, getToken, onMessage } = await import('firebase/messaging')
      const { initializeApp } = await import('firebase/app')
      console.log('✅ Firebase modules imported successfully')

      // Firebase config
      const firebaseConfig = {
        apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
        authDomain: "hlenergy-notifications.firebaseapp.com",
        projectId: "hlenergy-notifications",
        storageBucket: "hlenergy-notifications.firebasestorage.app",
        messagingSenderId: "506206785168",
        appId: "1:506206785168:web:5acaeacce5178fd2d45215",
        measurementId: "G-JEMVPQGQ5R"
      }

      // Initialize Firebase app
      console.log('🔥 Initializing Firebase app...')
      const app = initializeApp(firebaseConfig)
      console.log('✅ Firebase app initialized')

      // Initialize messaging
      console.log('🔥 Initializing Firebase messaging...')
      messaging = getMessaging(app)
      console.log('✅ Firebase messaging initialized')

      isSupported.value = true

      // Set up foreground message handler
      onMessage(messaging, (payload) => {
        console.log('🔥 [FCM] Foreground message received:', payload)
        
        // Show notification even when app is in foreground
        if (payload.notification) {
          const notificationTitle = payload.notification.title || 'HLenergy Notification'
          const notificationOptions = {
            body: payload.notification.body || 'You have a new notification',
            icon: payload.notification.icon || '/hl-energy-logo-192w.png',
            badge: payload.notification.badge || '/hl-energy-logo-96w.png',
            tag: payload.data?.tag || 'hlenergy-notification',
            data: payload.data || {},
            requireInteraction: payload.data?.requireInteraction === 'true'
          }

          // Show notification using Notification API for foreground
          if (Notification.permission === 'granted') {
            new Notification(notificationTitle, notificationOptions)
          }
        }
      })

      return messaging
    } catch (err) {
      console.error('🔥 Firebase messaging initialization failed:', err)
      error.value = err instanceof Error ? err.message : 'Failed to initialize messaging'
      throw err
    }
  }

  /**
   * Request notification permission
   */
  const requestPermission = async () => {
    try {
      console.log('🔥 Requesting notification permission...')
      const permission = await Notification.requestPermission()
      isPermissionGranted.value = permission === 'granted'

      console.log(`🔥 Permission result: ${permission}`)

      if (permission === 'denied') {
        error.value = 'Notification permission was denied. Please enable notifications in your browser settings.'
        console.warn('🔥 Permission denied - user must enable in browser settings')
        return false
      }

      if (permission === 'granted') {
        error.value = null
        console.log('✅ Permission granted successfully')
        return true
      }

      // Permission is 'default' - user dismissed the prompt
      console.log('⚠️ Permission prompt dismissed')
      return false
    } catch (err) {
      console.error('❌ Permission request failed:', err)
      error.value = err instanceof Error ? err.message : 'Permission request failed'
      return false
    }
  }

  /**
   * Get FCM token and subscribe to notifications
   */
  const subscribe = async () => {
    if (isLoading.value) return false
    
    isLoading.value = true
    error.value = null

    try {
      console.log('🔥 Starting FCM subscription...')

      // Check permission first
      if (Notification.permission !== 'granted') {
        const granted = await requestPermission()
        if (!granted) {
          throw new Error('Notification permission is required')
        }
      }

      // Initialize messaging if not already done
      if (!messaging) {
        await initializeMessaging()
      }

      // Get FCM token using the main service worker
      const { getToken } = await import('firebase/messaging')

      // Wait for the main service worker to be ready
      console.log('🔥 Waiting for service worker to be ready...')
      const swRegistration = await navigator.serviceWorker.ready
      console.log('✅ Service worker is ready:', swRegistration.scope)

      // Ensure the service worker is active
      if (!swRegistration.active) {
        throw new Error('No active service worker found')
      }

      console.log('🔥 Getting FCM token with main service worker...')
      const token = await getToken(messaging, {
        vapidKey: 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY',
        serviceWorkerRegistration: swRegistration
      })

      if (!token) {
        throw new Error('Failed to get FCM token')
      }

      console.log('🔥 FCM token obtained:', token.substring(0, 20) + '...')
      currentToken.value = token

      // Send token to backend
      await sendTokenToServer(token)

      isSubscribed.value = true
      console.log('✅ FCM subscription successful')
      
      return true
    } catch (err) {
      console.error('❌ FCM subscription failed:', err)
      error.value = err instanceof Error ? err.message : 'Subscription failed'
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Send FCM token to backend
   */
  const sendTokenToServer = async (token: string) => {
    try {
      console.log('📤 Sending FCM token to server...')

      const response = await fetch('/api/v1/push/subscribe/fcm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          token,
          deviceInfo: {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            deviceType: 'web'
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ FCM token sent to server successfully:', result)
      
      return result
    } catch (err) {
      console.error('❌ Failed to send FCM token to server:', err)
      throw err
    }
  }

  /**
   * Unsubscribe from notifications
   */
  const unsubscribe = async () => {
    try {
      if (!currentToken.value) return true

      // Delete token from backend
      await fetch('/api/v1/push/unsubscribe/fcm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          token: currentToken.value
        })
      })

      // Delete token from Firebase
      if (messaging) {
        const { deleteToken } = await import('firebase/messaging')
        await deleteToken(messaging)
      }

      currentToken.value = null
      isSubscribed.value = false
      console.log('✅ FCM unsubscription successful')
      
      return true
    } catch (err) {
      console.error('❌ FCM unsubscription failed:', err)
      error.value = err instanceof Error ? err.message : 'Unsubscription failed'
      return false
    }
  }

  /**
   * Check if already subscribed
   */
  const checkSubscription = async () => {
    try {
      if (!messaging) {
        await initializeMessaging()
      }

      // This will be implemented when we add FCM token storage to backend
      // For now, we'll assume not subscribed
      isSubscribed.value = false
      
      return isSubscribed.value
    } catch (err) {
      console.error('🔥 Failed to check FCM subscription:', err)
      return false
    }
  }

  /**
   * Initialize the messaging system
   */
  const initialize = async () => {
    try {
      console.log('🔥 Starting Firebase messaging initialization...')

      // First check browser support
      if (!('serviceWorker' in navigator)) {
        throw new Error('Service Worker not supported')
      }
      if (!('PushManager' in window)) {
        throw new Error('Push messaging not supported')
      }
      if (!('Notification' in window)) {
        throw new Error('Notifications not supported')
      }

      console.log('✅ Browser support checks passed')

      await initializeMessaging()
      isPermissionGranted.value = Notification.permission === 'granted'
      await checkSubscription()

      console.log('✅ Firebase messaging initialization complete')
    } catch (err) {
      console.error('❌ Failed to initialize Firebase messaging:', err)
      error.value = err instanceof Error ? err.message : 'Initialization failed'
      isSupported.value = false
    }
  }

  return {
    // State
    isSupported,
    isPermissionGranted,
    isSubscribed,
    currentToken,
    error,
    isLoading,

    // Computed
    canSubscribe,

    // Compatibility properties for NotificationManager
    subscription: currentToken, // Use currentToken as subscription
    permission,

    // Methods
    initialize,
    requestPermission,
    subscribe,
    unsubscribe,
    checkSubscription
  }
}
