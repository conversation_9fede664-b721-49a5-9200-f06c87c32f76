import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Firebase messaging composable for push notifications
export function useFirebaseMessaging() {
  const authStore = useAuthStore()
  
  // State
  const isSupported = ref(false)
  const isPermissionGranted = ref(false)
  const isSubscribed = ref(false)
  const currentToken = ref<string | null>(null)
  const error = ref<string | null>(null)
  const isLoading = ref(false)

  // Computed
  const canSubscribe = computed(() => 
    isSupported.value && isPermissionGranted.value && !isSubscribed.value
  )

  // Firebase messaging instance (lazy loaded)
  let messaging: any = null

  /**
   * Initialize Firebase messaging
   */
  const initializeMessaging = async () => {
    try {
      // Check if service worker and push messaging are supported
      if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
        throw new Error('Push messaging is not supported in this browser')
      }

      // Dynamic import Firebase messaging
      const { getMessaging, getToken, onMessage } = await import('firebase/messaging')
      const { initializeApp } = await import('firebase/app')

      // Firebase config
      const firebaseConfig = {
        apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
        authDomain: "hlenergy-notifications.firebaseapp.com",
        projectId: "hlenergy-notifications",
        storageBucket: "hlenergy-notifications.firebasestorage.app",
        messagingSenderId: "506206785168",
        appId: "1:506206785168:web:5acaeacce5178fd2d45215",
        measurementId: "G-JEMVPQGQ5R"
      }

      // Initialize Firebase app
      const app = initializeApp(firebaseConfig)
      
      // Initialize messaging
      messaging = getMessaging(app)
      
      isSupported.value = true
      console.log('🔥 Firebase messaging initialized')

      // Set up foreground message handler
      onMessage(messaging, (payload) => {
        console.log('🔥 [FCM] Foreground message received:', payload)
        
        // Show notification even when app is in foreground
        if (payload.notification) {
          const notificationTitle = payload.notification.title || 'HLenergy Notification'
          const notificationOptions = {
            body: payload.notification.body || 'You have a new notification',
            icon: payload.notification.icon || '/hl-energy-logo-192w.png',
            badge: payload.notification.badge || '/hl-energy-logo-96w.png',
            tag: payload.data?.tag || 'hlenergy-notification',
            data: payload.data || {},
            requireInteraction: payload.data?.requireInteraction === 'true'
          }

          // Show notification using Notification API for foreground
          if (Notification.permission === 'granted') {
            new Notification(notificationTitle, notificationOptions)
          }
        }
      })

      return messaging
    } catch (err) {
      console.error('🔥 Firebase messaging initialization failed:', err)
      error.value = err instanceof Error ? err.message : 'Failed to initialize messaging'
      throw err
    }
  }

  /**
   * Request notification permission
   */
  const requestPermission = async () => {
    try {
      const permission = await Notification.requestPermission()
      isPermissionGranted.value = permission === 'granted'
      
      if (permission === 'denied') {
        throw new Error('Notification permission denied')
      }
      
      return permission === 'granted'
    } catch (err) {
      console.error('🔥 Permission request failed:', err)
      error.value = err instanceof Error ? err.message : 'Permission request failed'
      return false
    }
  }

  /**
   * Get FCM token and subscribe to notifications
   */
  const subscribe = async () => {
    if (isLoading.value) return false
    
    isLoading.value = true
    error.value = null

    try {
      console.log('🔥 Starting FCM subscription...')

      // Check permission first
      if (Notification.permission !== 'granted') {
        const granted = await requestPermission()
        if (!granted) {
          throw new Error('Notification permission is required')
        }
      }

      // Initialize messaging if not already done
      if (!messaging) {
        await initializeMessaging()
      }

      // Get FCM token
      const { getToken } = await import('firebase/messaging')
      
      const token = await getToken(messaging, {
        vapidKey: 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY'
      })

      if (!token) {
        throw new Error('Failed to get FCM token')
      }

      console.log('🔥 FCM token obtained:', token.substring(0, 20) + '...')
      currentToken.value = token

      // Send token to backend
      await sendTokenToServer(token)

      isSubscribed.value = true
      console.log('✅ FCM subscription successful')
      
      return true
    } catch (err) {
      console.error('❌ FCM subscription failed:', err)
      error.value = err instanceof Error ? err.message : 'Subscription failed'
      return false
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Send FCM token to backend
   */
  const sendTokenToServer = async (token: string) => {
    try {
      console.log('📤 Sending FCM token to server...')

      const response = await fetch('/api/v1/push/subscribe/fcm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          token,
          deviceInfo: {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            deviceType: 'web'
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: { message: 'Unknown error' } }))
        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ FCM token sent to server successfully:', result)
      
      return result
    } catch (err) {
      console.error('❌ Failed to send FCM token to server:', err)
      throw err
    }
  }

  /**
   * Unsubscribe from notifications
   */
  const unsubscribe = async () => {
    try {
      if (!currentToken.value) return true

      // Delete token from backend
      await fetch('/api/v1/push/unsubscribe/fcm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.token}`
        },
        body: JSON.stringify({
          token: currentToken.value
        })
      })

      // Delete token from Firebase
      if (messaging) {
        const { deleteToken } = await import('firebase/messaging')
        await deleteToken(messaging)
      }

      currentToken.value = null
      isSubscribed.value = false
      console.log('✅ FCM unsubscription successful')
      
      return true
    } catch (err) {
      console.error('❌ FCM unsubscription failed:', err)
      error.value = err instanceof Error ? err.message : 'Unsubscription failed'
      return false
    }
  }

  /**
   * Check if already subscribed
   */
  const checkSubscription = async () => {
    try {
      if (!messaging) {
        await initializeMessaging()
      }

      // This will be implemented when we add FCM token storage to backend
      // For now, we'll assume not subscribed
      isSubscribed.value = false
      
      return isSubscribed.value
    } catch (err) {
      console.error('🔥 Failed to check FCM subscription:', err)
      return false
    }
  }

  /**
   * Initialize the messaging system
   */
  const initialize = async () => {
    try {
      await initializeMessaging()
      isPermissionGranted.value = Notification.permission === 'granted'
      await checkSubscription()
    } catch (err) {
      console.error('🔥 Failed to initialize Firebase messaging:', err)
    }
  }

  return {
    // State
    isSupported,
    isPermissionGranted,
    isSubscribed,
    currentToken,
    error,
    isLoading,
    
    // Computed
    canSubscribe,
    
    // Methods
    initialize,
    requestPermission,
    subscribe,
    unsubscribe,
    checkSubscription
  }
}
