import { ref, computed } from 'vue'
import { usePushNotifications } from './usePushNotifications'

export function usePostLoginModals() {
  // State
  const showPushModal = ref(false)

  // Composables
  const { isGranted, isSubscribed } = usePushNotifications()

  // Computed
  const shouldShowPushModal = computed(() =>
    !isSubscribed.value  // Show if not subscribed, regardless of permission status
  )

  // Methods
  const startPostLoginFlow = () => {
    console.log('🚀 Starting post-login push notifications flow...')

    // Debug information
    console.log('📊 Post-login flow conditions:', {
      shouldShowPushModal: shouldShowPushModal.value,
      isGranted: isGranted.value,
      isSubscribed: isSubscribed.value,
      notificationPermission: Notification?.permission || 'not-supported'
    })

    // Check if we need to show push notifications modal
    if (shouldShowPushModal.value) {
      console.log('🔔 Showing push notifications modal')
      setTimeout(() => {
        showPushModal.value = true
      }, 100)
    } else {
      console.log('✅ No modal needed - user already has notifications enabled')
    }
  }

  const handlePushEnabled = () => {
    console.log('✅ Push notifications enabled')
    showPushModal.value = false
  }

  const handlePushDismissed = () => {
    console.log('❌ Push notifications modal dismissed')
    showPushModal.value = false
  }

  const closePushModal = () => {
    showPushModal.value = false
  }

  // Reset flow
  const resetFlow = () => {
    showPushModal.value = false
  }

  return {
    // State
    showPushModal,

    // Computed
    shouldShowPushModal,

    // Methods
    startPostLoginFlow,
    handlePushEnabled,
    handlePushDismissed,
    closePushModal,
    resetFlow
  }
}
