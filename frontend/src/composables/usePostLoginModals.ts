import { ref, computed, onMounted, onUnmounted } from 'vue'
import { usePWAInstall } from './usePWAInstall'
import { usePushNotifications } from './usePushNotifications'

export function usePostLoginModals() {
  // State
  const showPWAModal = ref(false)
  const showPushModal = ref(false)
  const isFlowActive = ref(false)

  // Composables
  const { isInstalled, canInstall } = usePWAInstall()
  const { isGranted, isSubscribed } = usePushNotifications()

  // Computed
  const shouldShowPWAModal = computed(() => {
    // Show PWA modal if:
    // 1. App is not installed AND
    // 2. Either we can install (have deferred prompt) OR PWA is supported
    const pwaSupported = 'serviceWorker' in navigator && 'PushManager' in window
    return !isInstalled.value && (canInstall.value || pwaSupported)
  })

  const shouldShowPushModal = computed(() => 
    isInstalled.value && !isGranted.value && !isSubscribed.value
  )

  // Methods
  const startPostLoginFlow = () => {
    console.log('🚀 Starting post-login modal flow...')

    // Debug information
    const pwaSupported = 'serviceWorker' in navigator && 'PushManager' in window
    console.log('📊 Post-login flow conditions:', {
      isInstalled: isInstalled.value,
      canInstall: canInstall.value,
      pwaSupported,
      shouldShowPWAModal: shouldShowPWAModal.value,
      shouldShowPushModal: shouldShowPushModal.value,
      isGranted: isGranted.value,
      isSubscribed: isSubscribed.value
    })

    isFlowActive.value = true

    // Check what modals we need to show
    if (shouldShowPWAModal.value) {
      console.log('📱 Showing PWA installation modal')
      showPWAModal.value = true
    } else if (shouldShowPushModal.value) {
      console.log('🔔 Showing push notifications modal')
      showPushModal.value = true
    } else {
      console.log('✅ No modals needed - user already has PWA and notifications')
      isFlowActive.value = false
    }
  }

  const handlePWAInstalled = () => {
    console.log('✅ PWA installed - checking if push modal needed')
    showPWAModal.value = false
    
    // After PWA installation, show push notifications modal
    setTimeout(() => {
      if (shouldShowPushModal.value) {
        console.log('🔔 Showing push notifications modal after PWA install')
        showPushModal.value = true
      } else {
        console.log('✅ Push notifications already enabled')
        isFlowActive.value = false
      }
    }, 1000) // Small delay for better UX
  }

  const handlePWADismissed = () => {
    console.log('❌ PWA modal dismissed')
    showPWAModal.value = false
    isFlowActive.value = false
  }

  const handlePushEnabled = () => {
    console.log('✅ Push notifications enabled')
    showPushModal.value = false
    isFlowActive.value = false
  }

  const handlePushDismissed = () => {
    console.log('❌ Push notifications modal dismissed')
    showPushModal.value = false
    isFlowActive.value = false
  }

  const closePWAModal = () => {
    showPWAModal.value = false
    if (!showPushModal.value) {
      isFlowActive.value = false
    }
  }

  const closePushModal = () => {
    showPushModal.value = false
    isFlowActive.value = false
  }

  // Reset flow
  const resetFlow = () => {
    showPWAModal.value = false
    showPushModal.value = false
    isFlowActive.value = false
  }

  // Listen for PWA installation events
  const handlePWAInstalledEvent = (event: CustomEvent) => {
    console.log('🎉 PWA installed event received:', event.detail)
    if (isFlowActive.value) {
      handlePWAInstalled()
    }
  }

  // Set up event listeners
  onMounted(() => {
    window.addEventListener('pwa-installed', handlePWAInstalledEvent as EventListener)
  })

  onUnmounted(() => {
    window.removeEventListener('pwa-installed', handlePWAInstalledEvent as EventListener)
  })

  return {
    // State
    showPWAModal,
    showPushModal,
    isFlowActive,
    
    // Computed
    shouldShowPWAModal,
    shouldShowPushModal,
    
    // Methods
    startPostLoginFlow,
    handlePWAInstalled,
    handlePWADismissed,
    handlePushEnabled,
    handlePushDismissed,
    closePWAModal,
    closePushModal,
    resetFlow
  }
}
