<script setup lang="ts">
import { onMounted, onUnmounted, computed } from 'vue'
import { RouterView, useRouter, useRoute } from 'vue-router'
import DefaultLayout from './layouts/DefaultLayout.vue'
// Heavy components will be lazy loaded
import { defineAsyncComponent } from 'vue'

const PWAInstallPrompt = defineAsyncComponent(() => import('./components/PWAInstallPrompt.vue'))
const PWAUpdatePrompt = defineAsyncComponent(() => import('./components/PWAUpdatePrompt.vue'))
const NetworkStatus = defineAsyncComponent(() => import('./components/common/NetworkStatus.vue'))
const WhatsAppButton = defineAsyncComponent(() => import('./components/common/WhatsAppButton.vue'))
const VersionInfo = defineAsyncComponent(() => import('./components/common/VersionInfo.vue'))
const ToastContainer = defineAsyncComponent(() => import('./components/common/ToastContainer.vue'))
const SessionLock = defineAsyncComponent(() => import('./components/auth/SessionLock.vue'))
const GlobalCommunicationModal = defineAsyncComponent(() => import('./components/crm/GlobalCommunicationModal.vue'))
const ConsentBanner = defineAsyncComponent(() => import('./components/consent/ConsentBanner.vue'))
const PostLoginPushModal = defineAsyncComponent(() => import('./components/pwa/PostPWAPushModal.vue'))
import { usePWA } from './composables/usePWA'
import { useOfflineStore } from './stores/offline'
import { useOfflineCache } from './composables/useOfflineCache'
import { useSEO } from './composables/useSEO'
import { usePostLoginModals } from './composables/usePostLoginModals'
import { usePWAInstall } from './composables/usePWAInstall'
import { usePushNotifications } from './composables/usePushNotifications'
// Activity tracker will be lazy loaded
// Socket will be loaded lazily
// Performance monitor will be lazy loaded

const router = useRouter()
const route = useRoute()
const { initializePWA } = usePWA()
const offlineStore = useOfflineStore()
const { initializeCache, trackPageVisit } = useOfflineCache()
const { updateSEO } = useSEO()

// Post-login push notifications modal
const {
  showPushModal,
  startPostLoginFlow,
  handlePushEnabled,
  handlePushDismissed,
  closePushModal
} = usePostLoginModals()

// Activity tracking will be initialized lazily

// Check if current route is admin-related
const isAdminRoute = computed(() => {
  return route.path.startsWith('/admin')
})

// Socket will be loaded lazily
let globalSocket = null

// Global consent handler for cleanup
let consentUpdateHandler = null

// Auth login success handler for cleanup
const handleAuthLoginSuccess = (event) => {
  console.log('🔐 Auth login success detected, starting post-login flow', event.detail)

  // Add a small delay to ensure PWA systems are initialized
  setTimeout(() => {
    startPostLoginFlow()
  }, 500)
}

// Initialize PWA features
initializePWA()
offlineStore.initializeOfflineStore()

onMounted(() => {
  // Initialize SEO
  updateSEO()

  // Initialize offline cache
  initializeCache()

  // Listen for consent updates to immediately initialize analytics
  consentUpdateHandler = async (event) => {
    console.log('🔄 Global consent update detected', event.detail)
    if (event.detail?.preferences?.analytics) {
      try {
        // Dynamic import to avoid blocking
        const { reinitializeAnalytics } = await import('./plugins/firebase')
        await reinitializeAnalytics()
        console.log('🚀 Analytics initialized globally after consent')
      } catch (error) {
        console.warn('Failed to initialize analytics globally:', error)
      }
    }
  }

  window.addEventListener('consent-updated', consentUpdateHandler)
  window.addEventListener('auth-login-success', handleAuthLoginSuccess)

  // Debug functions for testing post-login flow
  if (import.meta.env.DEV) {
    window.testPostLoginFlow = () => {
      console.log('🧪 Testing post-login flow manually...')
      startPostLoginFlow()
    }

    window.clearPushDismissal = () => {
      localStorage.removeItem('push-post-pwa-dismissed')
      console.log('🧹 Cleared push notifications modal dismissal records')
    }

    window.debugPWAStatus = () => {
      const { isInstalled, canInstall, isInstallable } = usePWAInstall()
      console.log('🔍 PWA Status Debug:', {
        isInstalled: isInstalled.value,
        canInstall: canInstall.value,
        isInstallable: isInstallable.value,
        pwaSupported: 'serviceWorker' in navigator && 'PushManager' in window,
        userAgent: navigator.userAgent,
        displayMode: window.matchMedia('(display-mode: standalone)').matches ? 'standalone' : 'browser'
      })
    }

    window.forceShowPushModal = () => {
      console.log('🔧 Forcing push notifications modal to show...')
      showPushModal.value = true
    }

    window.debugModalStates = () => {
      console.log('🔍 Modal States:', {
        showPushModal: showPushModal.value
      })
    }

    window.debugPushStatus = () => {
      const { isGranted, isSubscribed } = usePushNotifications()
      console.log('🔍 Push Notification Status:', {
        isGranted: isGranted.value,
        isSubscribed: isSubscribed.value,
        notificationPermission: Notification?.permission || 'not-supported',
        shouldShowModal: !isSubscribed.value
      })
    }
  }

  // Lazy load heavy utilities
  setTimeout(async () => {
    try {
      // Load standalone activity tracker (no Vue dependencies)
      const activityTrackerModule = await import('./utils/activityTracker')
      const activityTracker = activityTrackerModule.default

      // Connect auth store to activity tracker
      const { useAuthStore } = await import('./stores/auth')
      const authStore = useAuthStore()
      activityTracker.setAuthStore(authStore)

      console.log('🔍 Activity tracking loaded lazily')

      // Load timer audit tool for development
      if (import.meta.env.DEV) {
        try {
          const timerAudit = await import('./utils/timerAudit')
          console.log('🔍 Timer audit tool loaded')

          // Print timer report every 3 minutes in development
          setInterval(() => {
            console.log('🔍 Timer Usage Report:')
            timerAudit.default.printReport()
          }, 3 * 60 * 1000)
        } catch (auditError) {
          console.warn('🔍 Timer audit tool failed to load:', auditError)
        }
      }
    } catch (error) {
      console.warn('Failed to load lazy utilities:', error)
    }
  }, 2000) // Load after 2 seconds

  // Track page visits for better caching
  router.afterEach((to) => {
    trackPageVisit(to.path)

    // Lazy load socket for analytics after initial render
    if (!globalSocket) {
      setTimeout(async () => {
        try {
          const { getGlobalSocket } = await import('./plugins/socket')
          globalSocket = getGlobalSocket()

          // Track this page view once socket is loaded
          if (globalSocket && globalSocket.trackPageView) {
            const pageTitle = to.meta?.title as string || document.title || to.name as string || to.path
            globalSocket.trackPageView(to.path, pageTitle)
          }
        } catch (error) {
          console.warn('Failed to load socket for analytics:', error)
        }
      }, 2000) // Load after 2 seconds
    } else if (globalSocket.trackPageView) {
      const pageTitle = to.meta?.title as string || document.title || to.name as string || to.path
      globalSocket.trackPageView(to.path, pageTitle)
    }
  })

  // Handle offline navigation errors
  router.onError((error) => {
    console.error('Router error:', error)

    if (error.message.includes('Failed to fetch dynamically imported module')) {
      console.log('Navigation failed - likely offline')

      if (!navigator.onLine) {
        // Show offline notification
        console.log('Redirecting to cached content')

        // Try to navigate to a cached page
        const cachedPages = ['/', '/about', '/services', '/contact', '/dashboard']
        const currentPath = window.location.pathname

        if (!cachedPages.includes(currentPath)) {
          router.replace('/')
        }
      }
    }
  })
})

onUnmounted(() => {
  // Clean up event listeners
  if (consentUpdateHandler) {
    window.removeEventListener('consent-updated', consentUpdateHandler)
  }

  window.removeEventListener('auth-login-success', handleAuthLoginSuccess)
})
</script>

<template>
  <DefaultLayout>
    <RouterView />

    <!-- PWA Components -->
    <PWAInstallPrompt />
    <PWAUpdatePrompt />
    <NetworkStatus />

    <!-- WhatsApp Contact Button (hidden on admin pages) -->
    <WhatsAppButton v-if="!isAdminRoute" />

    <!-- Version Information -->
    <VersionInfo />

    <!-- Toast Notifications -->
    <ToastContainer />

    <!-- Session Lock Modal -->
    <SessionLock />

    <!-- Global Communication Modal -->
    <GlobalCommunicationModal />

    <!-- Consent Banner -->
    <ConsentBanner />

    <!-- Post-Login Push Notifications Modal -->
    <PostLoginPushModal
      :show="showPushModal"
      @enabled="handlePushEnabled"
      @dismissed="handlePushDismissed"
      @close="closePushModal"
    />

  </DefaultLayout>
</template>
