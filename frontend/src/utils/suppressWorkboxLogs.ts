/**
 * Suppress Workbox Console Logs
 * 
 * This utility suppresses workbox and service worker related console logs
 * to reduce console noise in development and production.
 */

// Disable workbox dev logs globally
if (typeof globalThis !== 'undefined') {
  (globalThis as any).__WB_DISABLE_DEV_LOGS = true
}

if (typeof window !== 'undefined') {
  (window as any).__WB_DISABLE_DEV_LOGS = true
}

if (typeof self !== 'undefined') {
  (self as any).__WB_DISABLE_DEV_LOGS = true
}

/**
 * Suppress specific console methods for workbox-related messages
 */
export function suppressWorkboxLogs() {
  if (typeof console === 'undefined') return

  // Store original console methods
  const originalLog = console.log
  const originalInfo = console.info
  const originalWarn = console.warn
  const originalDebug = console.debug

  // Patterns to suppress
  const suppressPatterns = [
    /workbox/i,
    /service.?worker/i,
    /sw\.js/i,
    /precache/i,
    /cache.*strategy/i,
    /navigation.*route/i,
    /runtime.*caching/i,
    /background.*sync/i,
    /skip.*waiting/i,
    /clients.*claim/i,
    /^Workbox/,
    /^SW:/,
    /^Service Worker/,
    /precacheAndRoute/,
    /registerRoute/,
    /cleanupOutdatedCaches/,
    // Activity tracking patterns
    /activity tracking/i,
    /activity detected/i,
    /stopping activity/i,
    /starting activity/i,
    /🛑.*activity/i,
    /🔍.*activity/i,
    /🟢.*activity/i,
    /throttling cache attempt/i,
    /throttling navigation/i
  ]

  // Check if message should be suppressed
  const shouldSuppress = (message: string): boolean => {
    return suppressPatterns.some(pattern => pattern.test(message))
  }

  // Override console methods
  console.log = (...args: any[]) => {
    const message = args.join(' ')
    if (!shouldSuppress(message)) {
      originalLog.apply(console, args)
    }
  }

  console.info = (...args: any[]) => {
    const message = args.join(' ')
    if (!shouldSuppress(message)) {
      originalInfo.apply(console, args)
    }
  }

  console.warn = (...args: any[]) => {
    const message = args.join(' ')
    if (!shouldSuppress(message)) {
      originalWarn.apply(console, args)
    }
  }

  console.debug = (...args: any[]) => {
    const message = args.join(' ')
    if (!shouldSuppress(message)) {
      originalDebug.apply(console, args)
    }
  }

  // Return function to restore original console
  return () => {
    console.log = originalLog
    console.info = originalInfo
    console.warn = originalWarn
    console.debug = originalDebug
  }
}

/**
 * Suppress service worker registration logs
 */
export function suppressServiceWorkerLogs() {
  if (typeof navigator === 'undefined' || !('serviceWorker' in navigator)) {
    return
  }

  // Override service worker registration to suppress logs
  const originalRegister = navigator.serviceWorker.register
  
  navigator.serviceWorker.register = function(...args: any[]) {
    // Call original register but suppress success logs
    return originalRegister.apply(this, args).then((registration) => {
      // Suppress the default "Service Worker registered" log
      return registration
    }).catch((error) => {
      // Still show errors
      console.error('Service Worker registration failed:', error)
      throw error
    })
  }
}

/**
 * Initialize log suppression
 */
export function initializeLogSuppression() {
  // Set global flags
  if (typeof globalThis !== 'undefined') {
    (globalThis as any).__WB_DISABLE_DEV_LOGS = true
  }

  // Suppress workbox logs
  const restoreConsole = suppressWorkboxLogs()
  
  // Suppress service worker logs
  suppressServiceWorkerLogs()

  // Return cleanup function
  return restoreConsole
}

// Auto-initialize in development and production
if (typeof window !== 'undefined') {
  // Small delay to ensure workbox hasn't already started logging
  setTimeout(() => {
    initializeLogSuppression()
  }, 100)
}

export default {
  suppressWorkboxLogs,
  suppressServiceWorkerLogs,
  initializeLogSuppression
}
