import { fileURLToPath, URL } from 'node:url'
import { execSync } from 'child_process'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import vueDevTools from 'vite-plugin-vue-devtools'
import { VitePWA } from 'vite-plugin-pwa'
import compression from 'vite-plugin-compression'
import { visualizer } from 'rollup-plugin-visualizer'

// Get build information
const getBuildInfo = () => {
  try {
    const gitCommit = execSync('git rev-parse HEAD').toString().trim()
    const buildNumber = process.env.BUILD_NUMBER || Date.now().toString()
    return { gitCommit, buildNumber }
  } catch {
    return { gitCommit: 'unknown', buildNumber: 'local' }
  }
}

const { gitCommit, buildNumber } = getBuildInfo()

// https://vite.dev/config/
export default defineConfig({
  define: {
    __BUILD_DATE__: JSON.stringify('2025-06-06T16:11:17.563Z'),
    __GIT_COMMIT__: JSON.stringify(gitCommit),
    __BUILD_NUMBER__: JSON.stringify(buildNumber),
    // Disable workbox dev logs globally
    __WB_DISABLE_DEV_LOGS: true,
    // Timer audit disabled for cleaner console
    __ENABLE_TIMER_AUDIT__: JSON.stringify(false),
  },
  build: {
    target: 'esnext',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2,
      },
      mangle: {
        safari10: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Firebase in separate chunk (lazy loaded)
          if (id.includes('firebase')) {
            return 'firebase'
          }
          // Vue ecosystem
          if (id.includes('node_modules/vue') || id.includes('node_modules/@vue')) {
            return 'vue'
          }
          // Router and state management
          if (id.includes('vue-router') || id.includes('pinia')) {
            return 'router-state'
          }
          // UI libraries
          if (id.includes('daisyui') || id.includes('tailwindcss')) {
            return 'ui'
          }
          // Utilities
          if (id.includes('axios') || id.includes('socket.io')) {
            return 'utils'
          }
          // Analytics and tracking
          if (id.includes('analytics') || id.includes('tracking')) {
            return 'analytics'
          }
        },
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk'
          return `js/${chunkInfo.name || facadeModuleId}-[hash].js`
        },
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const ext = info[info.length - 1]
          if (/\.(css)$/.test(assetInfo.name)) {
            return `css/[name]-[hash].${ext}`
          }
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/.test(assetInfo.name)) {
            return `img/[name]-[hash].${ext}`
          }
          return `assets/[name]-[hash].${ext}`
        },
      },
    },
    chunkSizeWarningLimit: 500, // Reduced from 1000
    reportCompressedSize: false, // Faster builds
  },
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools(),
    // Gzip compression
    compression({
      algorithm: 'gzip',
      ext: '.gz',
      threshold: 1024, // Only compress files larger than 1KB
      deleteOriginFile: false,
    }),
    // Brotli compression (better than gzip)
    compression({
      algorithm: 'brotliCompress',
      ext: '.br',
      threshold: 1024,
      deleteOriginFile: false,
    }),
    // Bundle analyzer (only in build)
    process.env.ANALYZE && visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
    VitePWA({
      registerType: 'prompt',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,woff2,ttf,eot,webp,avif}'],
        globDirectory: 'dist',
        cleanupOutdatedCaches: true,
        skipWaiting: false,
        clientsClaim: false, // Disable to reduce conflicts
        navigateFallback: '/index.html',
        navigateFallbackDenylist: [/^\/_/, /\/[^/?]+\.[^/]+$/, /^\/api\//, /^\/sw\.js$/, /^\/workbox-/, /^\/firebase-messaging-sw\.js$/],
        // Disable workbox console logs completely
        mode: 'production',
        disableDevLogs: true,
        // Reduce caching aggressiveness
        offlineGoogleAnalytics: false,
        // Ensure main pages are cached for offline access
        additionalManifestEntries: [
          { url: '/', revision: null },
          { url: '/about', revision: null },
          { url: '/services', revision: null },
          { url: '/contact', revision: null },
          { url: '/dashboard', revision: null },
          { url: '/profile', revision: null },
          { url: '/crm', revision: null },
          { url: '/admin', revision: null }
        ],
        maximumFileSizeToCacheInBytes: 5000000, // 5MB
        runtimeCaching: [
          // Cache main pages for offline access (less aggressive)
          {
            urlPattern: /^https?:\/\/localhost:5174?\/(about|services|contact)$/,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'main-pages-cache',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 // 1 day only
              },
              networkTimeoutSeconds: 5
            }
          },
          // Cache Google Fonts
          {
            urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-stylesheets',
              expiration: {
                maxEntries: 10,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          },
          {
            urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'google-fonts-webfonts',
              expiration: {
                maxEntries: 30,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          },
          // Cache API calls
          {
            urlPattern: /^http:\/\/localhost:3001\/api\/.*/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'api-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              },
              networkTimeoutSeconds: 3
            }
          },
          // Cache images
          {
            urlPattern: /\.(?:png|jpg|jpeg|svg|gif|webp)$/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'images-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
              }
            }
          },
          // Cache CSS and JS files
          {
            urlPattern: /\.(?:css|js)$/i,
            handler: 'StaleWhileRevalidate',
            options: {
              cacheName: 'static-resources',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 * 7 // 7 days
              }
            }
          },
          // Cache HTML pages
          {
            urlPattern: /\.(?:html)$/i,
            handler: 'NetworkFirst',
            options: {
              cacheName: 'pages-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 60 * 24 // 24 hours
              },
              networkTimeoutSeconds: 3
            }
          },
          // Cache API responses with different strategies
          {
            urlPattern: /^http:\/\/localhost:3001\/api\/v1\/auth\/.*/i,
            handler: 'NetworkOnly', // Never cache auth requests
            options: {
              cacheName: 'auth-cache'
            }
          },
          {
            urlPattern: /^http:\/\/localhost:3001\/api\/v1\/(dashboard|analytics|stats)\/.*/i,
            handler: 'StaleWhileRevalidate',
            options: {
              cacheName: 'dashboard-cache',
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 60 * 60 // 1 hour
              }
            }
          },
          // Cache static content from CDNs
          {
            urlPattern: /^https:\/\/cdn\..*/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'cdn-cache',
              expiration: {
                maxEntries: 100,
                maxAgeSeconds: 60 * 60 * 24 * 365 // 1 year
              }
            }
          },
          // Cache video and audio files
          {
            urlPattern: /\.(?:mp4|webm|ogg|mp3|wav|flac|aac)$/i,
            handler: 'CacheFirst',
            options: {
              cacheName: 'media-cache',
              expiration: {
                maxEntries: 20,
                maxAgeSeconds: 60 * 60 * 24 * 30 // 30 days
              },
              rangeRequests: true
            }
          }
        ]
      },
      includeAssets: [
        'favicon-32x32.png',
        'favicon-16x16.png',
        'pwa-192x192.png',
        'pwa-256x256.png',
        'apple-touch-icon.png'
      ],
      manifest: {
        name: 'HLenergy - Energy Consultation Services',
        short_name: 'HLenergy',
        version: process.env.npm_package_version || '2.1.2',
        description: 'Professional energy consultation services to optimize your energy consumption and reduce costs',
        theme_color: '#1f2937',
        background_color: '#ffffff',
        display: 'standalone',
        scope: '/',
        start_url: '/',
        orientation: 'portrait-primary',
        categories: ['business', 'productivity', 'utilities'],
        lang: 'en',
        edge_side_panel: {
          preferred_width: 400
        },
        launch_handler: {
          client_mode: 'navigate-existing'
        },
        display_override: ['window-controls-overlay', 'standalone'],
        protocol_handlers: [
          {
            protocol: 'mailto',
            url: '/contact?email=%s'
          }
        ],
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: 'pwa-256x256.png',
            sizes: '256x256',
            type: 'image/png',
            purpose: 'any'
          },
          {
            src: 'pwa-256x256.png',
            sizes: '256x256',
            type: 'image/png',
            purpose: 'maskable'
          },
          {
            src: 'apple-touch-icon.png',
            sizes: '180x180',
            type: 'image/png',
            purpose: 'any'
          }
        ]
      },
      devOptions: {
        enabled: true,
        type: 'module'
      }
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    // Configure middleware to serve service workers with correct MIME type
    middlewareMode: false,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      }
    },
    // Configure headers for service workers
    headers: {
      'Service-Worker-Allowed': '/'
    }
  },
})
