<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase FCM Debug Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .error { color: red; }
        .success { color: green; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase FCM Debug Tool</h1>
        
        <div class="section">
            <h3>Firebase Status</h3>
            <button onclick="checkFirebaseStatus()">Check Firebase Status</button>
            <button onclick="requestPermission()">Request Permission</button>
            <button onclick="getToken()">Get FCM Token</button>
        </div>

        <div class="section">
            <h3>Test Notifications</h3>
            <button onclick="sendTestNotification()">Send Test Notification</button>
            <button onclick="testServiceWorker()">Test Service Worker</button>
        </div>

        <div class="section">
            <h3>Debug Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script type="module">
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyD4h0fiwHVOQrKR51UoDWIdbohcJnHanK4",
            authDomain: "hlenergy-notifications.firebaseapp.com",
            projectId: "hlenergy-notifications",
            storageBucket: "hlenergy-notifications.firebasestorage.app",
            messagingSenderId: "506206785168",
            appId: "1:506206785168:web:5acaeacce5178fd2d45215",
            measurementId: "G-JEMVPQGQ5R"
        };

        let messaging = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[FCM Debug] ${message}`);
        }

        window.clearLog = () => {
            document.getElementById('log').innerHTML = '';
        };

        window.checkFirebaseStatus = async () => {
            try {
                log('🔍 Checking Firebase status...');
                
                // Check browser support
                if (!('serviceWorker' in navigator)) {
                    log('❌ Service Worker not supported', 'error');
                    return;
                }
                if (!('PushManager' in window)) {
                    log('❌ Push messaging not supported', 'error');
                    return;
                }
                if (!('Notification' in window)) {
                    log('❌ Notifications not supported', 'error');
                    return;
                }

                log('✅ Browser supports all required features', 'success');
                log(`📱 Notification permission: ${Notification.permission}`);

                // Initialize Firebase
                const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                const { getMessaging } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js');
                
                const app = initializeApp(firebaseConfig);
                messaging = getMessaging(app);
                
                log('✅ Firebase initialized successfully', 'success');
                
            } catch (error) {
                log(`❌ Firebase initialization failed: ${error.message}`, 'error');
            }
        };

        window.requestPermission = async () => {
            try {
                log('🔔 Requesting notification permission...');
                const permission = await Notification.requestPermission();
                log(`📱 Permission result: ${permission}`, permission === 'granted' ? 'success' : 'error');
            } catch (error) {
                log(`❌ Permission request failed: ${error.message}`, 'error');
            }
        };

        window.getToken = async () => {
            try {
                if (!messaging) {
                    log('❌ Firebase not initialized. Run "Check Firebase Status" first.', 'error');
                    return;
                }

                log('🔑 Getting FCM token...');
                const { getToken } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js');
                
                const token = await getToken(messaging, {
                    vapidKey: 'BDZb2zuovpIdk2ji3585xQvFKYGoqk8eZQPX-1n2s9jlSPsDKwHkp7HaEOgctB0QadD1BjR6e4Ml_MG2vkoN-CY'
                });
                
                if (token) {
                    log(`✅ FCM Token: ${token}`, 'success');
                    navigator.clipboard.writeText(token).then(() => {
                        log('📋 Token copied to clipboard', 'success');
                    });
                } else {
                    log('❌ No registration token available', 'error');
                }
            } catch (error) {
                log(`❌ Token generation failed: ${error.message}`, 'error');
            }
        };

        window.sendTestNotification = () => {
            if (Notification.permission === 'granted') {
                new Notification('🔥 Test Notification', {
                    body: 'This is a test notification from the debug tool',
                    icon: '/hl-energy-logo-192w.png',
                    badge: '/hl-energy-logo-96w.png'
                });
                log('✅ Test notification sent', 'success');
            } else {
                log('❌ Notification permission not granted', 'error');
            }
        };

        window.testServiceWorker = async () => {
            try {
                log('🔧 Testing service worker...');
                const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
                log(`✅ Service worker registered: ${registration.scope}`, 'success');
            } catch (error) {
                log(`❌ Service worker registration failed: ${error.message}`, 'error');
            }
        };

        // Auto-initialize on load
        window.addEventListener('load', () => {
            log('🚀 FCM Debug Tool loaded');
            checkFirebaseStatus();
        });
    </script>
</body>
</html>
